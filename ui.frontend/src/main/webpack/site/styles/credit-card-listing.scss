.credit-card-listing__button {
  justify-content: center;
  border: 0.063rem solid var(--secondary-mid-grey-60);
  border-radius: 1.688rem;
  color: var(--primary-black);
  font-weight: 400;
  padding: 0.688rem 0.938rem;
  background-color: transparent;
  position: relative;
  display: inline-flex;
  outline: none;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
  transition: all 0.3s ease-in;
  align-items: center;
  grid-gap: 0.75rem;
  gap: 0.75rem;
  min-width: max-content;
  z-index: 1;
  line-height: 1.5rem;
  margin: 0;
  &:hover {
    background: var(--primary-black);
    color: var(--primary-white);
  }
  @include xs {
    padding: 0.438rem 0.938rem;
  }
}

.credit-card-listing__button.big-size {
  color: var(--body);
  font-size: 1rem;
  padding: 0.75rem 1.5rem;
}

.credit-card-listing__button.big-size.filter-selected,
.filter-selected {
  background-color: #000;
  color: var(--primary-white);
}

.credit-card-listing {
  min-height: 5.625rem;

  &__content {
    background-color: var(--primary-white);
    width: 100%;
    padding: 1.25rem 0;
    margin-bottom: 3rem;
  }

  &__container {
    display: flex;
    box-sizing: inherit;
    flex-grow: 0;
    flex-basis: 100%;
    align-items: center;
    flex-wrap: nowrap;
    overflow: hidden;
    h6 {
      margin-right: 1rem;
      white-space: nowrap;
      font-size: 1rem;
      font-weight: 600;
      line-height: 1.25rem;
    }
    .m-r-16 {
      margin-right: 1rem;
    }

    @include maxSm {
      h2 {
        min-width: fit-content;
      }
    }
  }

  &__items {
    display: flex;
    align-items: center;
    flex-grow: 1;
    flex-wrap: nowrap;
    overflow-x: scroll;
    gap: 1rem;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .list-card-product {
    &__container {
      width: 100%;
      display: block;
      box-sizing: border-box;
      margin-left: auto;
      margin-right: auto;

      .card-product-banner {
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;
      }
      @include maxMd {
        gap: 2.25rem;
      }
    }

    &__grid-container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      gap: 1.5rem;
      padding-bottom: 1.5rem;
    }

    &__grid-item {
      flex-grow: 0;
      max-width: calc((100% - 3rem) / 3);
      flex-basis: calc((100% - 3rem) / 3);
      @include maxMd {
        max-width: 48%;
        flex-basis: 48%;
      }

      @include maxLgSemi {
        .card-image {
          max-width: 100%;
        }
      }

      @include maxSm {
        max-width: 100%;
        flex-basis: 100%;
      }
    }
  }

  .card-bank {
    &__card {
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      border-radius: 0.5rem;
      transition: all 0.3s ease 0s;
      background-color: rgb(255, 255, 255);
      height: 100%;

      &:hover {
        box-shadow:
          rgba(0, 0, 0, 0.04) 0 33px 181px,
          rgba(0, 0, 0, 0.027) 0 13.7866px 75.6175px,
          rgba(0, 0, 0, 0.024) 0 7.37098px 40.4287px,
          rgba(0, 0, 0, 0.02) 0 4.13211px 22.664px,
          rgba(0, 0, 0, 0.016) 0 2.19453px 12.0367px,
          rgba(0, 0, 0, 0.01) 0 0.913195px 5.00873px;

        .card-bank__image {
          transform: translateY(-0.625rem);
        }
      }
    }

    &__button {
      position: relative;
      display: inline-flex;
      padding: 1rem 1.5rem;
      border-radius: 0.5rem;
      outline: none;
      border: none;
      cursor: pointer;
      white-space: nowrap;
      text-decoration: none;
      transition: all 0.3s ease-in 0s;
      justify-content: space-between;
      align-items: center;
      gap: 0.75rem;
      min-width: max-content;
      width: 100%;
      z-index: 1;
      background-color: inherit;

      &[data-button-compare] {
        &.disabled {
          color: var(--secondary-mid-grey-100);
          cursor: not-allowed;
          background-color: var(--cta-disabled);
          border: 0.063rem solid var(--cta-disabled);
        }

        &.selected {
          background-color: #e0f7e5;
          cursor: not-allowed;
          color: #1d6d30;

          .icon-svg-add {
            display: none;
          }

          .icon-svg-checked {
            display: block;
          }

          &:hover .icon-svg-checked {
            filter: grayscale(0);
          }
        }

        .icon-svg-checked {
          display: none;
        }

        .button__icon-svg {
          display: flex;
          align-items: center;
          transition: all 0.3s ease-in-out 0s;
        }
      }

      &-white {
        background-color: var(--primary-white);
        color: var(--primary-black);

        &:hover {
          background-color: var(--secondary-mid-grey-100);
          color: var(--secondary-grey-60);

          img {
            filter: grayscale(100%);
          }
        }
      }

      &__box-shadow {
        box-shadow:
          rgba(0, 0, 0, 0.04) 0px 33px 181px,
          rgba(0, 0, 0, 0.027) 0px 13.7866px 75.6175px,
          rgba(0, 0, 0, 0.024) 0px 7.37098px 40.4287px,
          rgba(0, 0, 0, 0.02) 0px 4.13211px 22.664px,
          rgba(0, 0, 0, 0.016) 0px 2.19453px 12.0367px,
          rgba(0, 0, 0, 0.01) 0px 0.913195px 5.00873px;
      }

      @include maxSm {
        padding: 0.75rem 1rem;
        white-space: normal;
      }
    }

    &__label {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      color: var(--primary-white);
      background-color: var(--primary-red);
      text-transform: uppercase;
      padding: 4px 1rem;
      border-radius: 0 0 0.5rem 0.5rem;
      white-space: nowrap;
      z-index: 1;
      font-weight: 600;
      font-size: 0.875rem;
      line-height: 1.5;
      letter-spacing: 0.125rem;
    }

    &__image-wrapper {
      padding: 3.125rem 3.5rem 2.5rem;
      border-radius: 0.5rem 0.5rem 0 0;
      overflow: hidden;
      min-height: 17.813rem;
      max-height: 17.813rem;
      @include maxSm {
        padding: 1.875rem 2.188rem 2.5rem;
        min-height: 13.75rem;
        max-height: 13.75rem;
      }
    }

    &__actions {
      margin-top: 1.5rem;
      display: flex;
      gap: 1.5rem;
      @include maxSm {
        gap: 0;
      }

      .cta-button {
        min-width: unset;
        max-width: 9.75rem;
        @include maxSm {
          padding: 1rem;
        }
        @include xs{
          padding: 1rem 1.5rem;
          gap: 0;
        }

        .cmp-button__text {
          font-weight: 600;
          white-space: break-spaces;
          text-align: center;
          font-size: 1rem;
          line-height: 1.5rem;

          @include maxSm2 {
            min-width: fit-content;
          }
        }

        &:hover:not(.cta-button--link) .cmp-button__icon {
          filter: brightness(0) invert(1);
        }
      }
      @media screen and (max-width: 1199px) and (min-width: 992px) {
        flex-direction: column;
        gap: 1rem;
      }
    }

    &__comparing-action {
      text-align: center;
      padding: 0px 1.5rem;
      margin-top: -1.063rem;
      position: relative;
      font-weight: 600;
    }

    &__image {
      display: flex;
      max-width: 100%;
      height: 100%;
      text-align: center;
      transition: all 0.3s ease 0s;
      transform: translateY(0px);
      border-radius: 0.75rem;
      place-content: center;
      img {
        border-radius: 0.75rem;
        object-fit: contain;
      }
    }

    &__name {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
      color: var(--primary-black);
      margin: 0;
      font-weight: 600;
      font-size: 1rem;
      line-height: 1.25;
    }

    &__info {
      padding: 1.5rem 1.5rem 2rem;
      flex: 1 1 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      @include xl {
        padding: 1.5rem 1.5rem 2rem;
      }
      @include maxSm {
        padding-left: 1rem;
        padding-right: 1rem;
      }
    }

    &__body-info {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    &__description {
      line-height: 1.5rem;
      color: var(--secondary-grey-60);
      margin-top: 1rem;

      ul {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      li {
        color: var(--secondary-grey-60);
        padding-left: 1.313rem;
        position: relative;

        &::before {
          position: absolute;
          content: '';
          width: 5px;
          height: 5px;
          background-color: var(--secondary-mid-grey-80);
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          border-radius: 1px;
        }
      }
    }
  }

  .compare-choosing {
    &__sticky-card {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      z-index: 1;
      background-color: var(--primary-white);
      padding: 1.25rem 0;
      transition: all 0.4s ease-in-out;
    }

    &__container {
      width: 100%;
      display: flex;
      box-sizing: inherit;
      margin-left: auto;
      margin-right: auto;
      align-items: center;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    &__list-card {
      display: flex;
      flex-grow: 0;
      max-width: 33.333333%;
      flex-basis: 30%;
    }

    &__list-card .has-card {
      border: none;
      flex: unset;
      max-width: 11rem;
      box-shadow: none;
    }

    @include maxMd {
      .compare-button {
        max-width: 100%;
        flex-basis: 100%;
        padding-top: 40px;

        &__link {
          padding: 0.75rem 1rem;
        }
      }
    }

    @include xs {
      &__sticky-card {
        padding: 1rem 0;
      }

      &__list-card {
        max-width: 100%;
        flex-basis: 100%;
        overflow-x: scroll;
        padding-top: 8px;
      }

      &__list-card::-webkit-scrollbar {
        display: none;
      }

      .remove-button {
        top: -8px;
        right: -8px;
      }
    }
  }

  .list-card__item {
    flex: 0 0 11rem;
    max-width: 11rem;
    height: 75px;
    border: 2px dashed var(--secondary-mid-grey-60);
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: inherit;

    &:not(:last-child) {
      margin-right: 1.5rem;
    }

    &-image {
      box-shadow:
        0 33px 181px rgb(0 0 0 / 4%),
        0 13.7866px 75.6175px rgb(0 0 0 / 3%),
        0 7.37098px 40.4287px rgb(0 0 0 / 2%),
        0 4.13211px 22.664px rgb(0 0 0 / 2%),
        0 2.19453px 12.0367px rgb(0 0 0 / 2%),
        0 0.913195px 5.00873px rgb(0 0 0 / 1%);
      border-radius: 6px;
      display: none;
      width: 100%;
      overflow: hidden;
      height: 100%;
      box-sizing: inherit;
    }

    &-image span {
      box-sizing: border-box;
      display: inline-block;
      overflow: hidden;
      width: 122.6px;
      height: 77.2px;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      position: relative;
    }

    &-image img {
      position: absolute;
      inset: 0px;
      box-sizing: border-box;
      padding: 0px;
      border: none;
      margin: auto;
      display: block;
      width: 0px;
      height: 0px;
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
      object-fit: contain;
      object-position: center center;
    }
  }

  .remove-button {
    width: 1.5rem;
    height: 1.5rem;
    background-color: var(--primary-red);
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
    position: absolute;
    top: -0.75rem;
    right: -0.75rem;
    cursor: pointer;
    box-sizing: inherit;

    img {
      width: 18px;
      height: 18px;
    }
  }

  .add-panel {
    width: 2rem;
    height: 2rem;
    background-color: var(--secondary-light-grey-60);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    box-sizing: inherit;

    img {
      filter: grayscale(100%);
    }
  }

  .has-card {
    .list-card__item-image {
      display: block;
    }

    .remove-button {
      display: flex;
    }

    .add-panel {
      display: none;
    }
  }

  .compare-button {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 23.6%;
    &__link {
      justify-content: space-between;
      background-color: var(--primary-black);
      color: var(--primary-white);
      position: relative;
      display: inline-flex;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease-in;
      align-items: center;
      grid-gap: 0.75rem;
      gap: 0.75rem;
      min-width: max-content;
      width: 100%;
      z-index: 1;
      font-weight: 600;
      line-height: 1.5rem;

      &[disabled] {
        color: var(--secondary-mid-grey-100);
        pointer-events: none;
        background-color: var(--cta-disabled);
        border: 1px solid var(--cta-disabled);
      }

      &:hover {
        background: var(--secondary-grey-60);
      }

      &-icon {
        display: flex;
        align-items: center;
        margin-left: 0;
        transition: all 0.3s ease-in-out;
        color: var(--primary-white);
        filter: brightness(0) invert(1);
      }
    }
  }
}

.credit-card-listing,
.credit-card-comparison-result {
  .popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    padding: 144px 0 146px;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.3s ease;

    &.open {
      opacity: 1;
      visibility: visible;
      pointer-events: auto;
      transition: all 0.3s ease;
    }

    &-content {
      padding: 50px 0 0;
      background: linear-gradient(90deg, #fdfbfb, #ebedee);
      position: relative;
      max-width: 1000px;
      min-height: 450px;
      border-radius: 1rem;
      @include maxSm {
        padding-top: 72px;
      }

      img {
        width: 1rem;
      }
    }

    .close {
      position: absolute;
      color: red;
      top: 1.25rem;
      right: 30px;
      cursor: pointer;
    }

    .popup-register {
      &_image {
        position: absolute;
        left: 1rem;
        top: 50%;
        width: 180px;
        height: auto;
        transform: translateY(-50%);

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center center;
        }
      }

      &_content {
        max-width: calc(100% - 196px);
        margin-left: auto;
      }

      &_contentTop {
        background-color: var(--primary-white);
        padding: 1rem 1.5rem;
        border-radius: 8px;
      }

      &_steps {
        display: flex;
        margin: 1rem -1.25rem 0;
        align-items: stretch;
      }

      &_item {
        padding: 0 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: auto;

        &.popup-register_step0 {
          display: none;
        }
      }

      &_titleDescOuter {
        margin-top: 1rem;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-shadow:
          0 33px 181px rgb(0 0 0 / 4%),
          0 13.7866px 75.6175px rgb(0 0 0 / 3%),
          0 7.37098px 40.4287px rgb(0 0 0 / 2%),
          0 4.13211px 22.664px rgb(0 0 0 / 2%),
          0 2.19453px 12.0367px rgb(0 0 0 / 2%),
          0 0.913195px 5.00873px rgb(0 0 0 / 1%); // Added by Adobe team
        border-radius: 1rem;
        img {
          width: 100%;
          object-fit: cover;
          object-position: center center;
          border-radius: 1rem;
        }
      }

      &_titleDesc {
        background-color: var(--primary-white);
        box-shadow:
          0 33px 181px rgba(0, 0, 0, 0.04),
          0 13.7866px 75.6175px rgba(0, 0, 0, 0.029),
          0 7.37098px 40.4287px rgba(0, 0, 0, 0.024),
          0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
          0 2.19453px 12.0367px rgba(0, 0, 0, 0.016),
          0 0.913195px 5.00873px rgba(0, 0, 0, 0.011);
        border-radius: 1rem;
        overflow: hidden;
        height: 100%;
      }

      &_contentBottom {
        display: flex;
        align-items: center;
        padding: 1rem;
      }

      &_title {
        margin-right: 33px;
      }

      &_linkGroup {
        display: flex;
      }

      &_text p {
        font-size: 1.5rem;
        line-height: 2.25rem;
        font-weight: 300;
      }

      &_linkItem {
        border-right: 1px solid #c4c4c4;
        padding-right: 1.5rem;
        margin-right: 1.5rem;

        a {
          justify-content: space-between;
          display: inline-flex;
          align-items: center;
          transition: all 0.3s ease-in-out;
          .link-text {
            // Added by Adobe team
            font-weight: 600;
          }
          &:hover {
            .link-text {
              text-decoration: underline;
            }
          }

          img {
            margin-left: 0.75rem;
          }
        }
      }

      @include md {
        &_item {
          &:first-child {
            flex: 0 0 16%;
            max-width: 16%;
          }
          h6 {
            // Added by Adobe team
            font-size: 1rem;
            line-height: 1.25rem;
            font-weight: 600;
          }
          &:nth-child(2) {
            flex: 0 0 24%;
            max-width: 24%;
          }

          &:nth-child(3),
          &:nth-child(4) {
            flex: 0 0 29%;
            max-width: 29%;
          }
        }
      }
      @include maxLgSemi {
        &_title {
          margin-right: 0.938rem;
          padding: 0;
        }
      }
      @include maxMd {
        &_image {
          display: none;
        }
        &_content {
          max-width: 100%;
        }
        &_contentTop {
          .popup-register_title {
            padding: 0;
            border: none;
          }
        }
        &_contentBottom {
          padding: 0 1rem 40px;
          display: block;
          background-color: var(--primary-white);
          width: 100%;
        }
        &_title {
          border-top: 1px solid #c4c4c4;
          padding-top: 40px;
        }
        &_linkGroup {
          display: block;
        }
        &_linkItem {
          display: block;
          margin-top: 1.5rem;
          border-right: none;
          padding-right: 0;
          margin-right: 0;
        }
      }
      @include maxSm {
        &_contentTop {
          padding: 0;
          background-color: transparent;

          .popup-register_title {
            padding: 0 1rem;
          }
        }

        &_desc {
          margin-top: 1.5rem;
          padding: 0 1rem;
          display: flex;
          align-items: center;
        }

        &_steps {
          margin: 0;
          overflow: auto;
          padding: 0 1rem 40px;
          background-color: var(--primary-white);
          width: 100vw;
        }
        &_item {
          padding: 0;
          margin-right: 1rem;
          min-width: 247px;

          &:first-child {
            display: none;
          }

          &.popup-register_step0 {
            display: block;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.07);
            border-radius: 1rem 1rem 0 0;
            background-color: var(--primary-white);
            padding: 40px 1rem;
            margin-top: 40px;
            margin-right: 0;

            .popup-register_titleDesc1 {
              margin-top: 1rem;
            }

            a {
              display: flex;
              padding: 0.75rem 1rem;
              border-radius: 8px;

              &:hover {
                background-color: var(--secondary-mid-grey-100);
                color: var(--secondary-grey-60);
              }
            }
          }
        }
      }
    }
  }
}
