/** Colors **/
:root {
  --accent: #ed1c24;
  --green: #32c959;
  --gray-900: #212121;
  --gray-600: #616161;
  --body: #000000;
  --light-secondary-text: #616161;
  --primary-background: #ffffff;
  --light-background-hover: #dedede;
  --light-border: #dedede;
  --priority-text: #ecd7b0;
  --header-height: 0px;
}

html,
body {
  width: 100%;
  -webkit-tap-highlight-color: transparent;
  background: #f5f6f8;
  font-size: 16px;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  touch-action: pan-y;
}

//Change from Adobe
a {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

a:hover button,
a:hover button span,
a:hover span {
  text-decoration: none;
}

/** TCB Common Styles **/
.tcb-container {
  max-width: 1314px;
  margin: 0 auto;
}

.tcb-button {
  display: inline-flex;
  padding: 16px 24px;
  border-radius: 8px;
  outline: none;
  cursor: pointer;
  gap: 12px;
  white-space: nowrap;
  font-size: 16px;
  font-weight: 600;
  border: 1px solid var(--primary-background);
  color: var(--primary-background);
  justify-content: space-between;
  transition: all 0.5s;
}

.tcb-button--medium {
  max-width: 328px;
  width: 100%;
}

.tcb-button.tcb-button--hover-lightgray:not(.priority-link):not(.priority-btn):not(.white-btn):hover {
  background-color: var(--light-secondary-text);
  fill: var(--primary-background);
  border: 1px solid var(--light-secondary-text);

  .tcb-arrow {
    filter: brightness(0) invert(1);
  }
}

.tcb-button.tcb-button--hover-lightgray:hover svg path {
  fill: #fff;
}

.tcb-button.tcb-button--hover-gray:hover {
  background-color: var(--gray-900) !important;
  fill: var(--primary-background);
}

.tcb-button.tcb-button--hover-gray:hover svg path {
  fill: #fff;
}

.teaser-button--medium {
  width: 234px;
}

.teaser-button--large {
  width: 310px;
}

.tcb-button.tcb-button--light {
  border: 1px solid currentColor;
  color: var(--body);
}

.tcb-button.tcb-button--dark {
  background-color: #000;
  color: #fff;
}

.tcb-button.tcb-button--dark:hover {
  background-color: #616161;
  color: #fff;
}

.tcb-button.tcb-button--dark:hover path {
  fill: #fff;
}

.tcb-button--link {
  display: inline-flex;
  border: none;
  padding: 0;
  border: 1px solid transparent;
  color: var(--body);
  .section-font-color & {
    color: inherit;
  }
}

.tcb-button:hover {
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.tcb-button.tcb-button--light:hover {
  border: 1px solid var(--body);
  background: var(--body);
  color: var(--primary-background);
}

.tcb-button.tcb-button--light:hover path {
  fill: var(--primary-background);
}

.tcb-button--use-icon {
  justify-content: space-between;
}

.tcb-icon {
  font-family: Material Icons;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.tcb-icon--action {
  cursor: pointer;
}

.tcb-icon.tcb-icon_close {
  opacity: 0.5;
  font-size: 24px;
}

.tcb-icon.tcb-icon_close:hover {
  opacity: 1;
}

.tcb-icon {
  display: inline-block;
}

.tcb-scroll-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  display: none;
}

.tcb-scroll-control.tcb-scroll-control_prev {
  transform: rotate(180deg) translateY(50%);
}

.can-prev .tcb-scroll-control_prev,
.can-next .tcb-scroll-control_next {
  display: flex;
}

.youtube-iframe {
  position: relative;
  &:before {
    padding-top: 56.25%;
    display: block;
    content: "";
  }
  > iframe {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
  }
}


/* Global CSS */
.tcb-teaser_background,
.tcb-hero-banner_background,
.more-info_background,
.match-parent {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.list-loan {
  line-height: 1.5;
}

.link_here {
  text-decoration: underline;
  color: blue;
  font-style: italic;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body.show-popup {
  height: 100vh;
  overflow: hidden;
}

body p {
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
}

.white-bg {
  background-color: #fff;
}

.origin-bg {
  background: #f5f6f8 !important;
}

.dark-gray-bg {
  background: #333;
  color: #fff;
}

/* Hide Element */
.hidden {
  display: none;
}

.hidden-forced {
  display: none !important;
}

.visibility-hidden {
  visibility: hidden;
}

.tooltiptext {
  .icon-info &, .tcb-tooltip & {
    display: none;
  }
}

.icon-info:not(.debt), .tcb-tooltip {
  cursor: pointer;
}

#tooltiptext {
  visibility: hidden;
  opacity: 0;
  position: fixed;
  z-index: 9999;

  .tooltiptext__content {
    bottom: 0px;
    background-color: #656565;
    color: #fff;
    position: absolute;
    z-index: 9999;
    padding: 8px;
    font-size: 13px;
    width: max-content;
    max-width: 188px;
    font-weight: 400;
    border-radius: 4px;
  }
  i {
    width: 24px;
    height: 12px;
    z-index: 9999;
    overflow: hidden;
    &::after {
      content: "";
      position: absolute;
      width: 12px;
      height: 12px;
      background-color: #656565;
      left: 50%;
      transform: translate(-50%, -50%) rotate(45deg);
    }
  }
}

/* Container CSS
commenting to fix section container enhancement ticket
.container {
  position: relative;
  max-width: 1920px;
  margin: 0 auto;
  min-height: auto;
}*/

.container .background-img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.black-text {
  color: #000 !important;
}

.white-text {
  color: white !important;
}

.gray-text {
  color: var(--light-secondary-text);
}

.center-text {
  text-align: center;
}

.black-bg {
  background-color: black !important;
}

.dark-gray {
  color: #c5c5c5;
}

.light-gray {
  color: #e3e4e6;
}

.darkest-gray {
  color: var(--gray-600);
}

.text-content {
  color: var(--gray-600);
  margin: 4px 0 0 16px;
  line-height: 1.5;
}

.container .background-img picture {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
}

.margin-top-48 {
  margin-top: 48px;
}

.padding-bottom-24 {
  padding-bottom: 24px !important;
}

.margin-bottom-24 {
  margin-bottom: 24px !important;
}

.background-mobile {
  display: none;
}

.container .background-img picture img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: 50% 30%;
}

.mobile-xl {
  display: none;
}

.bold-600 {
  font-weight: 600;
}

.box-shadow {
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
}

/* Header */
.global-header {
  position: sticky;
  width: 100%;
  top: 0;
  z-index: 15;
}

.header_layout {
  background-color: #f5f6f8;
}

.header_layout .header-navigation {
  width: 100%;
  z-index: 17;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 20px rgb(0 0 0 / 10%);
}

.header_layout .header-navigation:before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background-color: #fff; */
  z-index: -1;
  /* filter: drop-shadow(0 5px 20px rgba(0,0,0,.1)); */
}

.header_layout .navigation_primary {
  width: 100%;
  background-color: #f5f6f8;
  z-index: 2;
  padding-left: 48px;
  transition: all 0.3s linear 0s;
  &.hide-nav {
    height: 0;
    overflow: hidden;
    opacity: 0;
  }
  &.show-nav {
    height: 45px;
    overflow: unset;
    opacity: 1;
  }
}

.header_layout .navigation_primary-wrapper {
  display: flex;
  align-items: center;
  // Change from Adobe
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  // Change from Adobe
  height: 45px;
  padding: 12px 0;
}

// Change from Adobe
.header_layout.prelogin-header .navigation_primary-wrapper {
  height: 60px;
}

.header_layout .navigation-primary_left {
  display: flex;
  align-items: center;
  flex: 1;
}

.header_layout .navigation_primary-wrapper {
  padding: 0;
}

.header_layout .navigation-primary_left .navigation-primary_item {
  margin-right: 24px;
  cursor: pointer;
  position: relative;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  color: #a2a2a2;
  letter-spacing: normal;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  justify-content: flex-start;
}

// Change from Adobe
.header_layout .navigation-primary_left .navigation-primary_item.navigation-primary_item-dropdown_list {
  margin-right: 0;
  align-items: center;
  line-height: 1.313rem;
}

// Change from Adobe
.header_layout .navigation-primary_left .navigation-primary_item.navigation-primary_item-dropdown_list {
  padding: 12px 0;
}

.header_layout .navigation-primary_left .navigation-primary_item.active {
  font-weight: 700;
  color: #ed1c24;
}

// Change from Adobe
.header_layout .navigation-primary_left .navigation-primary_item.discover_dropdown_btn {
  margin-right: 16px;
  // Change from Adobe
  border-left: 1px solid #c5c5c5;
  padding-left: 24px;
  margin-left: 8px;
  padding-top: 0;
  padding-bottom: 0;
  font-weight: 400;
  line-height: 21px;
}

// Change from Adobe
.header_layout .navigation-primary_left .navigation-primary_item.discover_dropdown_btn .link_text {
  color: #a2a2a2;
}

.header_layout .navigation-primary_item-separator {
  width: 1px;
  height: 16px;
  background-color: #a2a2a2;
  cursor: unset;
}

.header_layout .mobile-button .link_component-link .header_layout .navigation-primary_left .link_component-link {
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: #a2a2a2;
  margin: 0;
  transition: all 0.3s ease;
  font-weight: 500;
  display: block;
  text-align: center;
  align-items: center;
  text-decoration: none;
  font-size: 12px;
  line-height: 16px;
}

.header_layout .navigation-primary_right {
  display: flex;
  // Change from Adobe
  margin: 0;
  align-items: center;
  position: relative;
  letter-spacing: 0.02em;
}

.header_layout .navigation-primary_right .navigation-primary_item {
  cursor: pointer;
  // Change from Adobe
  margin-right: 16px;
  position: relative;
  text-decoration: none;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  display: flex;
  align-items: center;
}



@media (max-width: 1024px) {
  .header_layout .navigation-primary_right .navigation-primary_item:last-child {
    border-right: none;
  }

  //Change from Adobe
  .navigation_sub_item.active {
    display: flex;
    overflow-y: scroll;
  }
}

.header_layout .link_component-link .active,
.header_layout .navigation-primary_item.active .link_text,
.header_layout .navigation-primary_left .link_component-link:hover,
.header_layout .navigation-primary_right .link_component-link:hover {
  color: #ed1c24;
  opacity: 1;
}

.navigation-primary_item.active::before {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  content: "";
  border-right: 16px solid transparent;
  border-left: 16px solid transparent;
  border-bottom: 16px solid #fff;
}

.navigation-primary_item-dropdown_list.open {
  background: #ffffff;
}

.navigation-primary_item-dropdown_list.open .dropdown-arrow {
  transform: rotate(180deg);
}

.navigation-primary_item-dropdown_list .header_list_dropdown {
  transform: translate3d(0, -80px, 0) scaleX(0) scaleY(0);
}

// Change from Adobe
.header_layout .navigation-primary_left .navigation-primary_item.navigation-primary_item-dropdown_list:hover .dropdown_holder,
.header_layout .navigation-primary_left .navigation-primary_item.navigation-primary_item-dropdown_list:hover .dropdown-arrow {
  opacity: 0.7;
}

// Change from Adobe
.header_layout .navigation-primary_left .navigation-primary_item .dropdown-arrow {
  color: #616161;
  font-size: 16px;
  transition: all 300ms ease;
}

.navigation-primary_left .navigation-primary_item.active::before {
  bottom: -16px;
}

.navigation-primary_right .navigation-primary_item.active::before {
  bottom: -12px;
}

.navigation-primary_item-dropdown_list.open .header_list_dropdown {
  display: block;
  min-width: 0px;
  background: #ffffff;
  padding: 4px 0;
  transform: translate3d(0, 0, 0) scaleX(1) scaleY(1);
}

// Change from Adobe
.navigation-primary_left .navigation-primary_item-dropdown_list.open .header_list_dropdown {
  padding: 16px;
}

.navigation-primary_item-dropdown_list .icon-back {
  transform: rotate(90deg);
}

.navigation-primary_item.show-active-arrow .link_component-link .link_text {
  color: #ed1c24;
}

.show-active-arrow {
  position: relative;
}

.show-active-arrow::after {
  content: "";
  position: absolute;
  background-color: #ffffff;
  width: 14px;
  height: 14px;
  transform: rotate(45deg) translate3d(-7px, 0, 0);
  bottom: -14px;
  // Change from Adobe
  left: 50%;
}


// Change from Adobe
.header_layout .navigation-primary_left .navigation-primary_item.navigation-primary_item-dropdown_list.show-active-arrow::after {
  bottom: -10px;
}

// Change from Adobe
.navigation-primary_left .navigation-primary_item.show-active-arrow:after {
  bottom: -24px;
  left: 40%;
}

// Change from Adobe
.navigation-primary_right .navigation-primary_item.show-active-arrow:after {
  bottom: -24px;
  left: 45%;
}

.header_layout .navigation-primary_right .link_component-link {
  color: var(--gray-600);
  justify-content: space-between;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
  font-size: 14px;
  font-weight: 400;
}

.header_layout .navigation_secondary {
  width: 100%;
  background-color: #fff;
}

.header_layout .navigation_secondary-wrapper {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  height: 56px;
}

.header_layout .header-logo {
  display: flex;
  padding: 12px 0;
  max-width: 232px;
  flex: 1;
}

.header_layout .header-logo .link_component-link {
  // Change from Adobe
  display: flex;
  align-items: center;
  transition: all 0.3s ease-in-out;
}

// Change from Adobe
.header_layout .header-logo .link_component-link picture {
  display: flex;
  width: 100%;
}

.header_layout .header-logo:not(.large-logo) .link_component-link img {
  width: 100%;
}
.header_layout .large-logo .link_component-link img {
  height: 37px;
}

.header_layout .navigation-secondary_menu {
  display: flex;
  padding: 0 20px;
  align-items: center;
  overflow: hidden;
  position: relative;
  padding-top: 0;
}

.header_layout .navigation-secondary_item {
  margin: 0 24px 0 0;
  padding: 12px 6px;
  position: relative;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: var(--gray-600);
  cursor: pointer;
  transition: all 0.3s ease;
  word-wrap: break-word;
  text-align: center;
  height: 100%;
  display: flex;
  align-items: center;
  text-decoration: none;
  box-sizing: border-box;
}

.header_layout .navigation-secondary_item:before {
  position: absolute;
  content: "";
  height: 4px;
  width: 0;
  background-color: #ed1c24;
  left: 0;
  bottom: 0;
  transition: all 0.3s ease;
}

.header_layout .navigation-secondary_item:hover:before {
  width: 100%;
}

.header_layout .priority__background-color .navigation-secondary_item:before {
  background-color: #ecd7b0;
}

.header_layout .navigation-secondary_item:not(.priority__font-color):hover {
  color: #000;
}

.header_layout .navigation-secondary_item:hover:before,
.header_layout .navigation-secondary_item.active:before,
.header_layout .navigation-secondary_item.priority-active:before {
  width: 100%;
}

.mobile-menu-items {
  .navigation-secondary_item.active a {
    font-weight: 600;
    color: #000;
  }
}

.navigation-secondary_item.active span {
  font-weight: 700;
  color: #000;
}

.navigation-secondary_item:not(.active) span {
  font-weight: 400;
  text-transform: capitalize;
  @media (max-width: 1024px) {
    .second-nav-600 & {
      font-weight: 600;
    }
    .second-nav-400 & {
      font-weight: 400;
    }
  }
}

.header_layout .navigation-secondary_actions {
  display: flex;
  align-items: center;
  box-sizing: border-box;

  &--mobile {
    display: none;

    @media (max-width: 1024px) {
      display: flex;
      align-items: center;

      .login-btn {
        &__text {
          color: var(--accent);
          font-size: 1rem;
          font-weight: 600;
          line-height: 1.313rem;
          letter-spacing: 0.125rem;
          cursor: pointer;
        }

        .dark &__text {
          color: #b4a393;
        }
      }

      .search-primary-btn {
        margin-right: 1rem;
      }
    }
  }
}

.header_layout .navigation-secondary_actions .link_component-link {
  background-color: #ed1c24;
  display: flex;
  align-items: center;
  height: 100%;
  padding-left: 16px;
  padding-right: 20px;
  color: #fff;
  font-size: 16px;
  line-height: 21px;
  letter-spacing: 2px;
  font-weight: 600;
  cursor: pointer;
  justify-content: space-between;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
  position: relative;

  &.alt-link {
    background-color: #000;
    display: none;
    transition: all .3s ease-in;
    padding: 16px 24px;
    line-height: 24px;

    @media (max-width: 1024px) {
      display: none;
    }

    &:hover {
      background-color: #616161;

      img {
        filter: brightness(0) invert(1);
      }
    }

    .navigation-secondary_actions-icon {
      span {
        letter-spacing: normal;
      }
      img {
        margin-inline: 16px 4px;
      }
    }

    .link_text:hover {
      text-decoration: none;
    }
  }

  @include maxSm {
    &.hide-on-mobile {
      display: none !important;
    }
  }
}

.header_layout .alternate-button-mobile {
  padding: 14px 16px;
  position: fixed;
  bottom: 0;
  z-index: 15;
  background-color: #fff;
  width: 100%;
  display: none;

  @include maxLgSemi {
    display: block;
  }
  .alt-link {
    padding: 12px 24px;
    border-radius: 8px;
    background-color: #000;
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 16px;
    padding-right: 20px;
    color: #fff;
    font-size: 16px;
    line-height: 21px;
    letter-spacing: 2px;
    font-weight: 600;
    cursor: pointer;
    justify-content: space-between;
    transition: all 0.3s ease-in-out;
    .link_text {
      width: 100%;
    }
    .navigation-secondary_actions-icon {
      display: flex;
      justify-content: space-between;
      span {
        letter-spacing: normal;
      }
    }
  }
}

.link_component-link:not(.active) .login__drop-down {
  height: 0;
}

.header_layout .login__drop-down {
  position: absolute;
  background: white;
  top: 100%;
  right: 0;
  z-index: 11;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-top: 8px;
  box-shadow: 0 33px 181px rgba(0, 0, 0, 0.04), 0 13.7866px 75.6175px rgba(0, 0, 0, 0.029),
    0 7.37098px 40.4287px rgba(0, 0, 0, 0.024), 0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
    0 2.19453px 12.0367px rgba(0, 0, 0, 0.016), 0 0.913195px 5.00873px rgba(0, 0, 0, 0.011);
}

.link_component-link.active .login__drop-down {
  height: auto;
}

.header_layout .login__drop-down .drop-down__item a {
  text-decoration: none;
  padding: 16px 0;
  margin: 0 16px;
  display: block;
  white-space: nowrap;
  color: #000;
}

.header_layout .drop-down__item:not(:last-child) a {
  border-bottom: solid 1px #a2a2a2;
}

.header_layout .navigation-secondary_actions .link_component-link:not(.active) .login__drop-down {
  display: none;
}

.navigation-primary_right .gray_text {
  color: #a2a2a2;
  opacity: 0.6;
}

.header_layout .navigation-primary_right .link_component-link.black_text {
  color: black;
  opacity: 1;
  font-weight: 600;
}

.header_layout .navigation-secondary_actions .link_text {
  display: inline-flex;
  align-items: center;
}

.header_layout .navigation-secondary_actions .link_text:hover {
  text-decoration: underline;
}

.header_layout .navigation-secondary_actions #login-btn .link_text:hover {
  text-decoration: none;
}

.header_layout .navigation-secondary_actions .navigation-secondary_actions-icon {
  display: flex;
  align-items: center;
}

.header_layout .navigation-secondary_actions .navigation-secondary_actions-icon img {
  margin-left: 8px;
}

.header_layout .hambuger-icon {
  display: none;
  flex-direction: column;
  justify-content: center;
  box-sizing: inherit;
  height: 100%;
  padding: 0 20px;
  border-left: solid 1px #f5f5f5;
}

.header_layout .hambuger-icon span {
  width: 16px;
  height: 2px;
  background-color: #c4c4c4;
  border-radius: 1px;
  margin-bottom: 2px;
  display: block;
  transition: all 0.3s ease;
  margin-top: 2px;
}

/* Header Language */
.header_layout .language_item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.header_layout .language_item .material-symbols-outlined {
  font-size: 16px;
  margin-right: -6px;
  color: black;
  opacity: 1;
}

.header_layout .material-symbols-outlined.active,
.header_layout .link_text.active {
  color: #ed1c24;
}

.header_layout .language_item .link_component-link {
  padding: 0 16px;
}

.header_layout .language_item .link_text {
  font-size: 12px;
}

.has-right-border {
  border-right: solid 1px #a2a2a2;
  padding-right: 16px;
}

.header_layout .language_item a.link_component-link:not(:last-child) {
  border-right: solid 1px #a2a2a2;
  line-height: 16px;
}

/* End Header Language */
.header_layout .link_component-link .material-symbols-outlined,
.header_layout .navigation-secondary_item .material-symbols-outlined {
  display: none;
}

.navigation_sub {
  width: 100%;
  top: 100px;
  position: fixed;
}

.navigation_sub_wraper {
  position: relative;
  top: 0;
  max-width: 90rem;
  padding-left: 4rem;
  padding-right: 4rem;
  margin: 0 auto;

  @include tabletPro {
    padding-left: calc(100% / 22.5);
    padding-right: calc(100% / 22.5);
  }
}

.navigation_sub_item {
  margin-top: 16px;
  display: none;
  color: #000;
  box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
  border: 1px solid #dde0e7;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
}

.navigation_sub_item.active {
  display: flex;
}

.navigation_tab_title {
  flex: 0 0 33.33%;
  max-width: 33.33%;
  padding: 26px 30px 48px 0;
  border-right: 1px solid #e3e4e6;
  display: flex;
  flex-direction: column;
}

.tab_item {
  padding: 12px 24px;
  width: 100%;
  overflow: hidden;
  transition: all 0.2s ease;
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
  justify-content: space-between;
  overflow: hidden;
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  @media (max-width: 1024px) {
    overflow: unset;
  }
}

.tab_item:hover {
  color: #fff;
  text-decoration: none;
  transition: all 1.5s ease;
  background: #000;
}

.tab_item .material-symbols-outlined {
  display: none;
  color: #ed1c24;
}

.tab_item:hover .material-symbols-outlined {
  display: block;
}

.navigation_tab_content {
  flex: 0 0 66.67%;
  max-width: 66.67%;
}

.tab_content {
  padding-top: 34px;
  display: none;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
}

.tab_content.active {
  display: flex;
}

.navigation_sub_item-viewall {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 40px;
  cursor: pointer;
  padding-left: 25px;
}

.navigation_sub_item-viewall .material-symbols-outlined {
  padding-left: 12px;
  color: #ed1c24;
}

.navigation_sub_tab_item.default-hide {
  display: none;
}

.navigation_sub_list.expanded .navigation_sub_tab_item.default-hide {
  display: flex;
}

.link_component-link.expanded {
  display: none;
}

.navigation_sub_list.expanded+.navigation_sub_item-viewall .link_component-link.expanded {
  display: inline;
}

.navigation_sub_list.expanded+.navigation_sub_item-viewall .link_component-link.collapsed {
  display: none;
}

.navigation_sub_list.expanded .material-symbols-outlined {
  transform: rotate(180deg);
}

.navigation_sub_item-viewall .link_component-link:hover {
  text-decoration: underline;
}

.navigation_sub_list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-left: 25px;
}

.navigation_sub_tab_item {
  flex: 0 0 50%;
  max-width: 50%;
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding-right: 90px;
}

.navigation_sub_item-image {
  margin-right: 8px;
  flex: 0 0 48px;
  max-width: 48px;
}

.navigation_sub_item-image img {
  width: 100%;
  height: auto;
}

.navigation_tab_bottom {
  background-color: #f5f6f8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
}

.navigation_tab_bottom .link_component-link {
  display: flex;
  align-items: center;
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.015em;
  font-weight: 700;
}

.navigation_tab_bottom .link_component-link img {
  margin-right: 16px;
}

.navigation_tab_bottom .link_component-link .material-symbols-outlined {
  display: flex;
  color: #ed1c24;
  padding-left: 16px;
}

.navigation_tab_bottom .link_component-link.no-content{
  @media (max-width: 1024px) {
    display: none;
  }
}

.navigation_sub_item-title {
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.015em;
  // Change from Adobe
  font-weight: 400;

  @media (max-width: 1024px) {
    font-weight: 700;
  }
}

.navigation_sub_item-des {
  // Change from Adobe
  font-size: 14px;
  line-height: 1;
  color: #aaa;
}

/** Header fixes **/

.header_layout .navigation_primary-wrapper {
  padding: 0;
}

.header_layout.prelogin-header .navigation_primary-wrapper {
  justify-content: space-between;
  gap: 24px;
}

.header_layout.prelogin-header .mobile-menu-items {
  display: none;
}

.navigation-primary_item-dropdown_list.open {
  background: #ffffff;
}

.language_dropdown_item.open .dropdown-arrow,
.navigation-primary_item-dropdown_list.open .dropdown-arrow {
  transform: rotate(180deg);
}

.navigation-primary_item-dropdown_list .header_list_dropdown {
  transform: translate3d(0, -80px, 0) scaleX(0) scaleY(0);
}

.language_dropdown_item .language_dropdown {
  transform: translate3d(0, -30px, 0) scaleX(0) scaleY(0);
}

.language_dropdown_item.open .language_dropdown,
.navigation-primary_item-dropdown_list.open .header_list_dropdown {
  display: block;
  min-width: 0px;
  padding: 10px 0;
  transform: translate3d(0, 0, 0) scaleX(1) scaleY(1);
}

.language_dropdown_item.open .language_dropdown {
  // Change from Adobe
  background: #ffffff;
  padding: 16px;
  width: max-content;
}

// Change from Adobe
.navigation-primary_item-dropdown_list.open .header_list_dropdown {
  padding: 0 0;
}

.navigation-primary_item-dropdown_list .icon-back {
  transform: rotate(90deg);
}

.language_dropdown .dropdown-item.active,
.language_dropdown .dropdown-item.active:hover {
  color: #ed1c24;
}

.language_dropdown .dropdown-item:hover,
.header_list_dropdown .dropdown-item:hover {
  color: #000000;
}

.language_dropdown .dropdown-item,
.header_list_dropdown .dropdown-item {
  padding: 0.25rem 0;
  font-size: 0.875rem;
  cursor: pointer;
  background-color: #fff;
  line-height: 21px;
  letter-spacing: 0.02em;
}

.language_dropdown .dropdown-item:hover,
.header_list_dropdown .dropdown-item:hover {
  color: #000;
}

.discover_tooltip {
  margin-bottom: 10px;
}

nav.header_layout {
  z-index: 16;
}

nav.header_layout .header-navigation {
  box-shadow: none;
}

.mobile-arrow-down {
  transform: rotate(90deg);
  z-index: 3;
}

.mobile-menu-dropdown.open .mobile-arrow-down {
  // Change from Adobe
  transform: rotate(180deg);
}

.navigation-primary_item.mobile-menu-dropdown .dropdown-item {
  padding: 16px;
}

.mobile-menu .discover_category {
  margin-top: 24px;
}

.category-link {
  font-size: 14px;
  font-weight: 600;
  color: #616161;
}

.hide-on-mobile {
  display: inherit;
}

.hide-on-desktop {
  display: none;
}

@media (min-width: 1025px) {
  .tab_item.active {
    color: #fff;
    text-decoration: none;
    transition: all 1.5s ease;
    background: #000;
  }

  .tab_item.active .material-symbols-outlined {
    display: block;
  }

  .header_layout.prelogin-header .navigation_secondary {
    display: none;
  }
}

/* Header mobile */
@media (max-width: 1024px) {
  .mobile-xl {
    display: block !important;
  }

  .header_layout {
    .header-navigation {
      position: sticky;
    }
  }

  .header_layout.prelogin-header .navigation_primary {
    display: none;
  }

  .header_layout.prelogin-header .navigation_primary-wrapper {
    justify-content: space-between;
    padding-right: 0 !important;
    // Change from Adobe
    width: fit-content;
    margin-left: 0;
  }

  .header_layout.prelogin-header .hambuger-icon {
    display: flex;
  }

  .header_layout.prelogin-header .mobile-menu .mobile-menu-items {
    display: block;
    flex: 1;
  }

  .header_layout.prelogin-header .language_dropdown_item .language_dropdown {
    width: 110px;
  }

  // .header_layout.prelogin-header .navigation-primary_right {
  // display: none;
  // }
  .header_layout.prelogin-header .navigation-primary_right .navigation-secondary_actions {
    display: none;
  }

  nav.header_layout {
    height: auto;
  }

  .mobile-menu {
    /* padding: 25px; */
    display: none;
    flex: 0 0 100%;
    max-width: 100%;
    height: auto;
  }

  .navigation_sub_wraper {
    padding-top: 32px;
    flex: 0 0 100%;
    max-width: 100%;
    background-color: #fff;
    padding-inline: 0;
    overflow: scroll;
  }

  .header_layout .navigation_secondary-wrapper {
    height: 64px;
  }

  .navigation_sub {
    position: fixed;
    width: 100%;
    left: 0;
    top: -100%;
    z-index: 0;
    height: calc(100% - 56px);
    background-color: #f5f6f8;
    margin-top: 0;
    padding-top: 0;
    transition: all 700ms ease;
  }

  .navigation_sub.active {
    display: flex;
    transform: translateX(0);
    transition: all 0.7s ease;
  }

  .navigation_sub.active .mobile-menu {
    display: flex;
    flex-direction: column;
    overflow: scroll;
    background-color: #fff;
  }

  .mobile-menu-items {
    background-color: #fff;
    // Change from Adobe
    padding-top: 24px;
    padding-bottom: 24px;
    padding-left: calc(100vw / 22.5);
    padding-right: calc(100vw / 22.5);
    height: fit-content;
    overflow-y: auto;
  }

  .navigation_sub.open_submenu {
    transform: translateX(-100%);
  }

  .open_submenu .navigation_tab_bottom {
    flex-direction: column;
    padding: 0;
    gap: 16px;
    grid-gap: 16px;
    align-items: flex-start;
    background-color: #fff;
  }

  .navigation_sub_item {
    box-shadow: none;
    border: none;
    background: none;
  }

  .header_layout .navigation-primary_left,
  .header_layout .navigation-primary_right {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    margin: 0;
    .search-primary-btn {
      display: none;
    }
  }

  .navigation-primary_item.active::before {
    display: none;
  }

  .header-text {
    font-size: 14px;
    line-height: 21px;
    margin-right: 8px;
    color: #000;
    text-transform: capitalize;
    font-weight: 400;
    white-space: nowrap;
    padding: 0 0 0 16px;
    letter-spacing: 2px;
  }

  .header-text.align-center {
    align-self: center;
  }

  .header-text:not(.align-center) {
    padding-top: 20px;
  }

  .header_layout .mobile-button {
    border: 1px solid #f2f2f2;
    align-items: flex-start;
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #f2f2f2;
    // box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
    padding: 0 !important;
  }

  .header_layout .navigation-secondary_item:before {
    background: none !important;
  }

  .header-navigation.active .hambuger-icon span:first-child {
    transform: translateY(6px) rotate(45deg);
  }

  .header-navigation.active .hambuger-icon span:nth-child(2) {
    opacity: 0;
  }

  .header-navigation.active .hambuger-icon span:nth-child(3) {
    transform: translateY(-6px) rotate(-45deg);
  }

  .header-navigation:not(.active) {
    transform: scaleY(65px);
    transition: transform 0.3s ease-out;
  }

  .header-navigation.active {
    position: relative;
    z-index: 17;
    box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
    transform: scaleY(1);
    transition: transform 0.3s ease-out;
  }

  .header-navigation .navigation_primary,
  .header-navigation .navigation-secondary_menu {
    display: none;
  }

  .mobile-expand {
    border: 1px solid #f2f2f2;
    border-radius: 8px;
    background-color: #fff;
    position: absolute;
    top: 0;
    left: 0;
    box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
    z-index: 2;
    width: 100%;
    opacity: 0;
    transform: scale(0.75, 0.5625);
    transition: opacity 324ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 216ms cubic-bezier(0.4, 0, 0.2, 1) 108ms;
    visibility: hidden;
  }

  .mobile-main-nav.active .mobile-expand {
    display: flex;
    opacity: 1;
    visibility: visible;
    transform: none;
    transition: opacity 324ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 216ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  }

  .mobile-main-nav {
    position: relative;
    //  Change from Adobe
    padding-top: 32px;
    padding-bottom: 32px;
    padding-left: calc(100vw / 22.5);
    padding-right: calc(100vw / 22.5);
    background-color: #f5f6f8;
  }

  .mobile-main-nav .mobile-button {
    display: flex;
  }

  .header_layout.prelogin-header .mobile-main-nav .mobile-button {
    margin-bottom: 0;
  }

  .header_layout .navigation-primary_item .link_component-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    // Change from Adobe
    font-size: 14px;
    font-weight: 600;
    line-height: 21px;
  }

  .header_layout .navigation-primary_item .material-symbols-outlined {
    margin-right: 23px;
  }

  .header_layout .header-navigation .navigation_secondary {
    order: -1;
  }

  .header_layout .navigation-primary_item-separator {
    display: none;
  }

  .navigation_secondary .navigation-primary_right {
    display: flex;
    margin: 0;
  }

  .navigation_secondary .navigation-primary_right .link_text {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }

  .has-right-border,
  .navigation-primary_right .navigation-primary_item:not(.language_item) {
    border: none;
  }

  .header_layout .navigation-secondary_menu {
    flex-direction: column;
    align-items: flex-start;
    padding: 0;
    width: 100%;
    // Change from Adobe
    margin-bottom: 16px;
  }

  .header_layout .navigation-primary_item:not(.navigation-primary_item-separator):not(.language_item),
  .header_layout .navigation-secondary_menu .navigation-secondary_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 100%;
    width: 100%;
    color: #000;
  }

  // Change from Adobe
  .header_layout .navigation-primary_item {
    margin-bottom: 24px;
  }

  //Change from Adobe
  .header_layout .discover_category .navigation-primary_item {
    margin-bottom: 0;
  }

  // Change from Adobe
  .header_layout .navigation-secondary_menu .navigation-secondary_item {
    padding: 16px 0;
  }

  // Change from Adobe
  .header_layout .navigation-secondary_menu .navigation-secondary_item a {
    width: fit-content;
    font-weight: 600;
    line-height: 24px;
    white-space: nowrap;
  }

  .header_layout .navigation-primary_item a.link_component-link {
    text-decoration: none;
    color: var(--gray-600);
  }

  .header_layout .navigation-primary_left .navigation-primary_item:not(:last-child),
  .header_layout .navigation-secondary_item {
    border-bottom: 1px solid #e3e4e6;
  }

  .header_layout .header-logo:not(.large-logo) {
    max-width: 35px;
    padding: 8px 0;
  }

  .header_layout .header-navigation .navigation_secondary-wrapper {
    padding-right: 0 !important;
  }

  .header_layout .navigation-secondary_actions .link_component-link {
    background: none;
  }

  .header_layout .navigation-secondary_actions .link_component-link .link_text {
    color: #ed1c24;
  }

  .header_layout .navigation_secondary-wrapper .hambuger-icon {
    display: flex;
  }

  .navigation-primary_left .navigation-primary_item.navigation-primary_item-separator,
  .header_layout .navigation-secondary_actions .navigation-secondary_actions-icon {
    display: none;
  }

  .header_layout .language_item .material-symbols-outlined {
    font-size: 20px;
    margin: 0px;
  }

  .header_layout .language_item .link_text {
    font-size: 16px;
  }

  .mobile-main-nav .link_component-link .material-symbols-outlined,
  .header_layout .navigation-secondary_item .material-symbols-outlined {
    display: block;
    color: #ed1c24;
  }

  .header_layout .navigation-primary_right .language_item {
    padding-top: 16px;
  }

  .language_item .link_component-link:not(.gray_text) .link_text {
    color: black;
  }

  .header_layout .language_item .link_component-link {
    padding: 0 10px;
  }

  .navigation_tab_title {
    border: none;
    flex: 1;
    max-width: 100%;
    z-index: 100;
    padding: 0 16px;
  }

  .tab_item {
    background-color: #fff;
    box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
    border-radius: 8px;
    padding: 24px 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
    text-transform: capitalize;
  }

  .tab_item .material-symbols-outlined {
    display: block;
  }

  .tab_item:hover {
    background: #fff;
    color: #000;
  }

  .navigation_tab_content {
    display: none;
  }

  .navigation_sub_tab_item {
    flex: 100%;
    max-width: 100%;
    padding-right: 0;
    width: 100%;
    padding-bottom: 17px;
    margin-bottom: 16px;
    border-bottom: 1px solid #e3e4e6;
  }

  .navigation_sub_list {
    padding-left: 0;
    margin-top: 24px;
    width: 100%;
  }

  .back_menu {
    display: flex;
    font-size: 16px;
    line-height: 24px;
    text-transform: capitalize;
    font-weight: 700;
    margin-bottom: 32px;
    padding: 0 16px;
  }
}

/*header inspire */
.header_layout.inspire .navigation_secondary {
  background-color: rgb(255, 255, 255);
}

.header_layout.inspire .navigation_secondary-wrapper {
  height: 68px;
}

.header_layout.inspire .header-logo {
  max-width: 119px;
}

.header_layout.inspire .header-logo .link_component-link {
  height: 100%;
}

.header_layout.inspire .navigation-secondary_menu-inspire div {
  display: flex;
}

.header_layout.inspire .navigation-secondary_menu-inspire {
  display: flex;
}

.header_layout.inspire .navigation-secondary_actions .link_component-link {
  font-weight: 400;
  letter-spacing: 0.32px;
}

.header_layout.inspire .navigation-secondary_actions .link_component-link:hover span {
  text-decoration: none;
}

.header_layout.inspire .header-popup {
  display: none;
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 133px 0;
}

.header_layout.inspire .header-popup-content {
  background-color: #fefefe;
  width: 100%;
  max-width: 1236px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: repeat(12, minmax(0, 1fr));
  margin-left: auto;
  margin-right: auto;
}

.header-popup-content .left-content {
  grid-column: span 7 / span 7;
  padding: 60px 48px;
}

.header-popup-content .left-content h3 {
  font-size: 32px;
  line-height: 45px;
  margin: 0 0 24px 0;
}

.header-popup-content .left-content .identity {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header-popup-content .identity svg {
  margin-right: 16px;
}

.header-popup-content .identity span {
  font-size: 24px;
}

.header-popup-content .radio-btn .radio {
  margin-bottom: 16px;
}

.header-popup-content .radio-btn .step-guide {
  display: none;
}

.header-popup-content .radio-btn .step-guide a {
  color: rgb(76 153 230);
  cursor: pointer;
}

.header-popup-content .radio-btn .radio label {
  font-size: 24px;
  letter-spacing: 0;
  margin-bottom: 8px;
  position: relative;
  display: flex;
}

.header-popup-content .radio-btn .radio label span {
  padding-left: 40px;
}

.header-popup-content .radio-btn .radio input {
  display: none;
}

.header-popup-content .radio-btn .radio label:before {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  content: "";
  background: #fff;
  border: 1px solid rgb(237 27 36);
}

.header-popup-content .radio-btn .radio input:checked~label::after {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 3px;
  content: "";
  background: #ed1b24;
}

.header-popup-content .radio-btn ul {
  list-style: none;
  padding-right: 16px;
  padding-left: 42px;
}

.header-popup-content .radio-btn li {
  padding: 20px 24px;
  border-bottom: 1px solid rgb(222 222 222);
  display: flex;
  align-items: center;
}

.header-popup-content .radio-btn li span {
  font-size: 36px;
  font-weight: 600;
  margin-right: 24px;
}

.header-popup-content .radio-btn li:last-child {
  border-bottom: none;
}

.header-popup-content .radio-btn li p {
  font-size: 18px;
  line-height: 24px;
  font-weight: 600;
}

.header-popup-content .radio-btn li picture {
  display: flex;
  justify-content: center;
  flex: 0 0 82px;
}

.header-popup-content .radio-btn li img {
  object-fit: contain;
  border-radius: 8px;
  width: 100%;
}

.header-popup-content .radio-btn .have-account p {
  font-size: 18px;
  padding-left: 42px;
  color: var(--gray-900);
}

.header-popup-content .right-content {
  grid-column: span 5 / span 5;
  position: relative;
  display: flex;
  justify-content: center;
}

.header-popup-content .right-content .background {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
}

.header-popup-content .right-content .background img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.header-popup-content .right-content .image {
  max-width: 328px;
  width: auto;
  height: auto;
  margin-top: 146px;
  position: relative;
}

.header-popup-content .right-content .header-close-form {
  position: absolute;
  z-index: 2;
  right: 20px;
  top: 15px;
  cursor: pointer;
}

.header-popup-content .right-content .form {
  padding: 48px 32px 32px;
  align-items: center;
  justify-content: center;
  width: 100%;
  display: flex;
  position: relative;
}

.header-popup-content.hide-form .image {
  display: block;
}

.header-popup-content.hide-form .form,
.header-popup-content.hide-form .step-guide {
  display: none !important;
}

.header-popup-content.show-form .image {
  display: none;
}

.header-popup-content.show-form .form,
.header-popup-content.show-form .step-guide {
  display: flex;
}

.header-popup-content .right-content .form-content {
  background-color: white;
  width: 100%;
  height: 100%;
  padding: 32px;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
}

.header-popup-content .right-content .form-content h3 {
  font-weight: 300;
  margin: 0 0 24px 0;
}

.header-popup-content .right-content .form-content label {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
  position: relative;
}

.header-popup-content .form-content input[type="text"] {
  padding: 16px;
  border: 1px solid rgb(227 228 230);
  border-radius: 8px;
  width: 100%;
  margin-bottom: 16px;
  outline: none;
}

.header-form-accept_condition {
  margin-bottom: 16px;
}

.header-form-accept_condition input {
  display: none;
}

.header-form-accept_condition label {
  padding-left: 32px;
  text-transform: unset;
}

.header-form-accept_condition label a {
  color: rgb(237 27 36);
}

.header-form-accept_condition label:before {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 20%;
  transform: translateY(-50%);
  left: 0;
  content: "";
  background: #fff;
  border: 1px solid rgb(237 27 36);
  border-radius: 6px;
  cursor: pointer;
}

.header-form-accept_condition input:checked~label::after {
  width: 23px;
  height: 23px;
  position: absolute;
  text-align: center;
  top: 20%;
  transform: translateY(-50%);
  left: 0;
  content: url(https://api.iconify.design/charm:tick.svg?color=%23ffffff);
  background: rgb(237 27 36);
  border-radius: 6px;
  cursor: pointer;
}

.header-popup-content .form-content button {
  color: rgb(255 255 255);
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  padding: 16px 24px;
  background-color: rgb(33 33 33);
  border-radius: 7px;
  max-width: 302px;
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 32px;
  justify-content: space-between;
  cursor: pointer;
}

.header-popup-content .form-content button svg {
  transition-duration: 300ms;
}

.header-popup-content .form-content button:hover svg {
  transform: translate(10px);
}

/** range percent */
.slide-container {
  width: 100%;
  position: relative;
  padding-bottom: 58px;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
}

.save-calculation .slide-container {
  padding-bottom: 38px;
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  border-radius: 5px;
  background: #d3d3d3;
  outline: none;
  opacity: 0.7;
  -webkit-transition: 0.2s;
  transition: opacity 0.2s;
  background-image: linear-gradient(#ed1c24, #ed1c24);
  background-size: 0 100%;
  background-repeat: no-repeat;
  cursor: pointer;
}

.slider:hover {
  opacity: 1;
}

.loan-realestate__container .slider {
  opacity: 1;
}

.loan-realestate__container .slider:hover {
  opacity: 1;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  box-shadow: 0 0 3px 0 rgb(0 0 0 / 25%);
}

.slider::-moz-range-thumb {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  box-shadow: 0 0 3px 0 rgb(0 0 0 / 25%);
}

.percent-range {
  display: flex;
  justify-content: space-between;
  top: 20px;
  position: absolute;
  font-size: 0.875rem;
  width: 100%;
  font-weight: 400;
  line-height: 1.43;
  white-space: nowrap;
  letter-spacing: 0.01071em;
  color: #a2a2a2;
}

.percent-value {
  width: 60px;
  height: 30px;
  border-radius: 16px;
  background-color: #fff;
  transform: unset;
  filter: drop-shadow(0 -1px 3px rgba(0, 0, 0, 0.14));
  align-items: center;
  justify-content: center;
  display: flex;
  font-size: 0.75rem;
  z-index: 2;
  position: absolute;
  font-weight: 400;
  line-height: 1.2;
  letter-spacing: 0.01071em;
  transform-origin: bottom center;
  top: 33px;
  transform: translateX(-16px);
  user-select: none;
}

.percent-value::before {
  position: absolute;
  content: "";
  top: -8px;
  left: 25px;
  right: 0;
  border-color: transparent transparent #fff;
  border-style: solid;
  border-width: 0 5px 8px;
  width: 0;
  height: 0;
  filter: drop-shadow(0 -1px 3px rgba(0, 0, 0, 0.14));
  z-index: 1;
}

/** end range */

@media (max-width: 992px) {
  .header_layout .navigation_primary-wrapper {
    width: calc(100% - 88px);
  }
}

/* End header */
/* Footer */
.footer-container {
  background-color: rgb(33, 33, 33);
  box-sizing: inherit;
  padding-top: 32px;
  padding-bottom: 32px;
  max-width: 1920px;
  margin: 0 auto;
}

.footer-links {
  color: rgb(162, 162, 162);
  // Adobe Change
  // padding-bottom: 12px;
  // margin: -12px;
  margin: 0pt;
  padding-bottom: 0pt;
}

.footer-links li {
  list-style: none;
  padding-top: 8px;
  line-height: 24px;
}

.footer-links li a {
  text-decoration: none;
  color: rgb(162, 162, 162);
  line-height: 24px;
  display: block;
}

.footer-links .footer-links__item p {
  color: rgb(255, 255, 255);
  font-size: 16px;
  margin: 0;
  font-weight: 700;
}

.footer-links__list {
  max-height: 0;
  overflow: hidden;
  position: relative;
  display: flex;
  transition: max-height 0.3s ease-in-out;
}

.footer-links__list.expanded {
  max-height: 1200px;
  transition: max-height 0.3s ease-in-out;
}

.footer-links__list ul {
  padding: 0;
}

.footer-links__item {
  //change by Adobe
  flex-basis: 16.666%;
  padding: 12px;
}

.footer-info {
  padding-top: 24px;
  color: #a2a2a2;
  border-top: 1px solid #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  grid-gap: 32px;
  gap: 32px;

  @media (max-width: 767px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.footer-info p,
.footer-info a {
  font-size: 14px;
  line-height: 21px;
  font-weight: 400;
  align-self: flex-start;
  line-height: 21px;
  .dark & {
    color: inherit;
  }
}

.footer-logo {
  // Change from Adobe
  padding-top: 0;
  padding-bottom: 0;
}

.footer-logo img {
  width: 280px;
}

.footer-head {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  // Change from Adobe
  align-items: center;
}

.footer-head .footer-expand {
  // Change from Adobe
  height: fit-content;
  color: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;

  img {
    transition: all 300ms ease;
  }
}

.footer-expand svg {
  width: 16px;
  transition: transform 0.4s;
}

.footer-expand span {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5;
  // Change from Adobe
  // padding-right: 10px;
  padding-right: 12px;
}

.footer-expand:hover {
  cursor: pointer;
  text-decoration: underline;
}

.footer-links__social-container {
  display: flex;
  flex-direction: column;
  color: #a2a2a2;
  padding: 12px 0;
  // Change from Adobe
  padding-top: 8px;
}

.footer-links__social-media {
  display: flex;
  margin-top: 12px;
  margin-left: -16px;
}

.footer-links__social-item {
  margin-left: 16px;
}

.footer-links__social-item img {
  width: 24px;
}

.footer-container .footer-wrapper {
  box-sizing: inherit;
  display: flex;
  flex-direction: column;
}

.footer-links__social-container {
  align-items: end;
}

/* End of Footer */

/* End Container CSS */

/* Component CSS */
@-webkit-keyframes curExSlideIn {
  from {
    bottom: -500px;
    opacity: 0;
  }

  to {
    bottom: 0;
    opacity: 1;
  }
}

@keyframes curExSlideIn {
  from {
    bottom: -500px;
    opacity: 0;
  }

  to {
    bottom: 0;
    opacity: 1;
  }
}

@-webkit-keyframes curExFadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes curExFadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* Text Image Vertical */
.content-container {
  min-height: 100%;
}

.no-min-height {
  min-height: unset !important;
}

.overflow-hidden,
.dropdown-overflow-hidden,
.modal-showing {
  overflow: hidden;
}

.object-position-80-50 {
  object-position: 80% 50% !important;
}

.bg-255 {
  background-color: rgb(255, 255, 255);
}

.display-flex-center {
  display: flex;
  justify-content: center;
}

.pad-0 {
  padding: 0;
}

.position-relative {
  position: relative;
}

.max-width-1920 {
  max-width: 1920px;
}

.max-width-1440 {
  max-width: 14400px;
}

.max-width-570 {
  max-width: 570px;
}

.mag-auto {
  margin: 0 auto;
}

.pad-bot-12 {
  padding-bottom: 12px;
}

.content-container .content-container__header {
  color: #000;
  padding-bottom: 12px;
}

.mar-left-0 {
  margin-left: 0;
}

.mar-top-8 {
  margin-top: 8px !important;
}

.mar-top-24 {
  margin-top: 24px;
}

.mar-vertical-36 {
  margin-top: 36px;
  margin-bottom: 36px;
}

.pad-vertical-36 {
  padding-top: 36px;
  padding-bottom: 36px;
}

.pad-vertical-40 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.pad-vertical-48 {
  padding-top: 48px;
  padding-bottom: 48px;
}

.display-flex {
  display: flex;
}

.flex-space-between {
  justify-content: space-between;
}

.flex-start {
  justify-content: flex-start;
}

.flex-content {
  justify-content: center;
}

.align-items {
  align-items: center;
}

.pad-bot-8 {
  padding-bottom: 8px;
}

.flex-wrap {
  flex-wrap: wrap;
}

.content-container .content-container__title h2 {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
}

.color-gray {
  color: var(--gray-600);
}

.color-white {
  color: white;
}

.color-red {
  color: red;
}

.color-240-75-60 {
  color: hsl(240, 75%, 60%);
}

.mar-bot-12 {
  margin-bottom: 12px;
}

.margin-bot-64 {
  margin-bottom: 64px;
}

.margin-top-8 {
  margin-top: 8px !important;
}

.no-padding {
  padding: 0 !important;
}

.padding-left-0 {
  padding-left: 0px !important;
}

.padding-top-12 {
  padding-top: 12px !important;
}

.width-100 {
  width: 100%;
}

.mar-top-36 {
  margin-top: 36px;
}

.mar-bot-36 {
  margin-bottom: 36px;
}

.min-height-580 {
  min-height: 580px;
}

.blue-underline a {
  color: blue !important;
  text-decoration: underline !important;
  font-weight: 400 !important;
}

.mar-vertical-32 {
  margin-top: 32px;
  margin-bottom: 32px;
}

.mar-left-auto {
  margin-left: auto;
}

.mar-right-auto {
  margin-right: auto;
}

.content-container .content-container__body {
  max-width: 1920px;
}

.border-radius-8 {
  border-radius: 8px;
}

.no-border {
  border: none !important;
}

@media (max-width: 767px) {
  .no-padding-bot-mobile {
    padding-bottom: 0 !important;
  }

  .no-padding-top-mobile {
    padding-top: 0 !important;
  }
}


/* End of Text Image Vertical Normal Media */
/* Text Image Vertical Special Media */

@media (max-width: 575px) {
  .margin-mobile-0 {
    margin: 0 !important;
  }
}

@media (max-width: 767px) {

  /* Footer Small*/
  .footer-head {
    // Change from Adobe
    align-items: flex-start;
    flex-direction: column;
  }

  .footer-expand {
    order: -1;
  }

  .footer-links {
    order: -1;
  }

  .footer-links__social-container {
    align-items: flex-start;
  }

  .footer-links__list {
    flex-wrap: wrap;
  }

  .footer-info p,
  .footer-info a {
    min-width: 100%;
  }
}

/* List Tile Large(old)*/
*,
::after,
::before {
  box-sizing: inherit;
}

.list-tile-small__header-title {
  padding-bottom: 20px;
}

.list-tile-small__header .list-tile-small__small {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  position: relative;
}

.list-tile-small__list-tiles {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  margin: 0px auto;
  width: 100%;
  gap: 24px;
}

.list-tile-small__tiles-item {
  display: block;
  position: relative;
  width: 100%;
  border-radius: 8px;
}

.list-tile-small__tile-wide::before {
  content: "";
  width: 1px;
  margin-left: -1px;
  float: left;
  height: 0px;
  padding-bottom: 80%;
}

.tile_small_list-tile-small::before {
  content: "";
  width: 1px;
  margin-left: -1px;
  float: left;
  height: 0px;
  padding-bottom: 40%;
}

.list-tile-small__tiles-item::after {
  content: "";
  display: table;
  clear: both;
}

.list-tile-small__article {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  overflow: hidden;
  border-radius: 8px;
  width: 100%;
  height: 100%;
  transition: box-shadow 0.3s ease 0s;
}

.list-tile-small__article:hover {
  box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 8px;
}

.list-tile-small__article picture {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
}

.list-tile-small__article picture img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: 80% 50%;
  border-radius: 8px;
  z-index: 0;
}

.list-tile-small__article .hero__content-wrapper .hero__content-wrapper-inner {
  padding: 16px 24px;
}

.list-tile-small__article .hero__content-wrapper .hero__card-body .hero__card-content {
  width: 100%;
  position: relative;
  z-index: 1;
}

.hero__card-title {
  font-weight: 350;
  font-size: 1.5rem;
  line-height: 1.5;
}

.hero__card-description {
  font-weight: 400;
  font-size: 1rem;
}

.list-tile-small__article .hero__content-wrapper .hero__card-title {
  width: calc(100% - 24px);
}

.list-tile-small__article .hero__content-wrapper .hero__card-body .hero__card-content .hero__card-description {
  margin-top: 8px;
  width: calc(100% - 24px);
}

.hero__nav-text {
  font-weight: 600;
  font-size: 1rem;
}

.list-tile-small__article .hero__content-wrapper .hero__card-body .hero__nav-text {
  display: inline-flex;
  align-items: center;
  margin-top: 8px;
}

.list-tile-small__article .hero__content-wrapper .hero__card-body .hero__nav-text .hero__nav-icon {
  position: relative;
  margin-left: 12px;
  display: flex;
}

.hero__nav-icon .icon-image {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  width: 16px;
  height: 17px;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: relative;
  margin-top: 5px;
}

.list-tile-small .hero-item {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
}

@media (max-width: 1199px) {
  .list-tile-small {
    margin-top: 36px;
    margin-bottom: 36px;
  }
}

@media (min-width: 992px) {
  .list-tile-small__list-tiles {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(calc(25% - 24px), 1fr));
    grid-auto-flow: column;
    grid-auto-rows: 1fr 1fr;
  }

  .list-tile-small__list-tiles .list-tile-small__list-tiles-grid {
    padding-bottom: 40%;
  }
}

@media (max-width: 992px) {
  .list-tile-small {
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}

@media (max-width: 992px) {
  .list-tile-small__list-tiles {
    gap: 24px;
    flex-wrap: nowrap;
  }
}

@media (max-width: 992px) {
  .list-tile-small__list-tiles-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .list-tile-small__tiles-item {
    grid-column-end: span 2;
    grid-row-end: span 2;
  }

  .tile_small_list-tile-small::before {
    padding-bottom: 40%;
  }
}

@media (min-width: 576px) {
  .list-tile-small__grid-left-item {
    grid-column-end: span 2;
    grid-row-end: span 1;
  }

  .list-tile-small .list-tile-small__square {
    grid-row-end: span 1;
    grid-column-end: span 1;
  }
}

@media (min-width: 576px) {
  .list-tile-small__grid-left-item.list-tile-small__tile-wide {
    grid-column-end: span 2;
    grid-row-end: span 2;
  }
}

@media (max-width: 768px) {
  .list-tile-small__list-tiles-grid {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: stretch;
    margin: 0 auto;
    width: 100%;
    grid-gap: 16px;
    gap: 16px;
  }

  .hero__background-image .hidden {
    display: none;
  }

  .list-tile-small__tile-wide::before {
    background-image: url(https://d1kndcit1zrj97.cloudfront.net/uploads/Frame_46430_fc5313394c.png?w=1920&q=75);
    border-radius: 8px;
    padding-bottom: 125%;
  }

  .tile_small_list-tile-small::before {
    padding-bottom: 125%;
  }

  .list-tile-small__article .hero__background-image {
    border-radius: 8px;
    z-index: 0;
    background-image: url(https://d1kndcit1zrj97.cloudfront.net/uploads/Card_Mobile_a7b6ca646d.png);
    background-repeat: no-repeat;
  }

  .hero__background-image .hidden {
    display: none;
  }

  .hero__background-image-2 {
    background-position: 80% 50%;
    border-radius: 8px;
    z-index: 0;
    background-image: url(https://d1kndcit1zrj97.cloudfront.net/uploads/Rectangle_19369_edb5d954ab.png);
    background-repeat: no-repeat;
  }

  .hero__background-image-3 {
    background-position: 80% 50%;
    border-radius: 8px;
    z-index: 0;
    background-image: url(https://d1kndcit1zrj97.cloudfront.net/uploads/Tai_Khoan_Overview_banner_3_mobile_685973356f.png);
    background-repeat: no-repeat;
  }
    .hide-on-mobile {
      display: none;
    }

    .hide-on-desktop {
      display: inherit;
    }
}

/* End of List Tile Large(old)*/

/* New Hero */
.hero-cmp {
  position: relative;
  padding: 0;
  display: flex;
  align-items: center;
  min-height: 216px;
  flex-direction: column;
}

.hero-cmp.large {
  min-height: 480px;
}

.hero-cmp.normal {
  min-height: 400px;
}

.hero-cmp.small {
  min-height: 280px;
}

.hero-wrapper {
  overflow: hidden;
  display: flex;
  width: 100%;
  position: relative;
  flex: 1 1;
  flex-direction: column;
  width: 100%;
  justify-content: center;
}

.hero-cmp .background-container,
.hero-cmp .background-img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  display: block;
}

.hero-cmp .background-container {
  border: 0px;
  margin: 0px;
  width: initial;
  height: initial;
  overflow: hidden;
  background: none;
  opacity: 1;
}

.hero-cmp .background-img {
  border: none;
  margin: auto;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: 80% 50%;
}

.hero-simple {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  flex: 1 1;
  width: 100%;
}

.insurance .hero-simple {
  justify-content: center;
}

.title-tab-center .hero-item {
  margin-top: 24px;
}

.hero-cmp .hero-item {
  flex-direction: column;
}

.hero-item .hero-label {
  position: absolute;
  display: block;
  top: 65px;
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 2px;
}

.homepage .hero-item {
  margin-top: 60px;
}

.title-only-center .hero-item {
  margin-top: 70px;
}

.hero-section.hero-dark .hero-item-medium>h1 {
  color: #fff;
}

.hero-section .hero-button__url a {
  justify-content: space-between;
  background-color: #000;
  color: #fff;
  position: relative;
  display: inline-flex;
  padding: 16px 24px;
  border-radius: 8px;
  outline: none;
  border: none;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
  transition: all 0.3s ease-in;
  align-items: center;
  grid-gap: 12px;
  gap: 12px;
  min-width: max-content;
  width: 100%;
  z-index: 1;
}

.hero-button__url.no-icon-arrow.business-button {
  padding-bottom: 36px;
  padding-top: 8px;
}

.hero-description {
  margin-top: -10px;
  padding-left: 94px;
  color: var(--gray-600);
}

.hero-description.no-icon-arrow {
  padding-left: unset;
  padding-top: 16px;
}

.hero-title-container {
  display: flex;
}

.hero-title-container .hero-title {
  font-size: 2rem;
  font-weight: 300;
  padding-top: 48px;
  line-height: 40px;
}

.dark-bg .hero-title,
.dark-bg .hero-description {
  color: #fff;
}

.hero-description>p {
  font-size: 1.5rem;
}

.loan .hero-item {
  margin-top: 20px;
}

.loan .hero-description {
  padding-left: 0;
  margin-top: 8px;
}

/* Hero icon */

.hero-icon-arrow {
  align-items: normal;
  margin-top: 24px;
  height: 90px;
  display: flex;
  margin-right: 32px;
  max-width: 62px;
  flex: 0 0 62px;
}

.hero-icon-arrow>span {
  overflow: hidden;
}

/* end of Hero icon */

/* Hero Button */
.hero-button__url {
  padding-left: 94px;
  padding-bottom: 48px;
  padding-top: 32px;
  max-width: 328px;
}

.hero-button__url.no-icon-arrow {
  padding-left: unset;
  padding-top: 16px;
}

.insurance .hero-button__url {
  padding-bottom: 0;
}

.loan .hero-button__url {
  padding-left: 0;
}

.hero-button__url a {
  max-width: 265px;
  display: flex;
  justify-content: space-between;
  background-color: #000;
  color: #fff;
  padding: 16px 24px;
  border-radius: 8px;
  outline: none;
  border: none;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease-in;
  align-items: center;
  width: 100%;
  line-height: 1.5;
  font-weight: 600;
}

.hero-button__url.large a {
  max-width: inherit;
}

.hero-button__url.small-btn a {
  max-width: 100%;
}

.hero-button__url a:hover {
  background-color: rgb(97, 97, 97);
  color: rgb(255, 255, 255);
}

.hero-button__url a span {
  padding-right: 30px;
}

.hero-button__url a .hero-button__icon,
.hero-link__url a .hero-button__icon {
  display: flex;
  align-items: center;
  margin-left: 0;
  transition: all 0.3s ease-in-out;
}

.hero-link__url a {
  max-width: 265px;
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  color: #fff;
  outline: none;
  border: none;
  cursor: pointer;
  width: 100%;
}

.hero-link__url a:hover {
  text-decoration: underline;
}

/* end of Hero Button */

/* Hero Tab Navigation */
.hero-wrapper-tab {
  display: flex;
  padding: 0 64px;
  margin-top: auto;
  max-width: 1440px;
  width: 100%;
  z-index: 5;
}

.hero-wrapper-tab.full-width {
  padding: 0 !important;
}

.hero-tab-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 0;
}

.hero-tab-grid {
  background-color: #fff;
  display: flex;
  padding: 0 34px;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.hero-tab-item {
  padding: 8px 12px 16px;
  height: 106px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  cursor: pointer;
  color: #a2a2a2;
  font-weight: 300;
  min-width: 100px;
  transition: all 0.3s ease;
  position: relative;
  text-align: center;
}

.hero-tab-item>p {
  white-space: nowrap;
}

.hero-cmp .hero-tab-item>.inner-item>span>img {
  filter: saturate(0) brightness(150%);
  width: 40px;
  height: 40px;
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.3s ease;
}

.hero-cmp .active>.inner-item>span>img {
  filter: none !important;
}

.hero-tab-item.active {
  background-color: #f2f2f2;
  border-top: 4px solid #ed1b24;
  color: black;
}

.hero-tab-item.active p {
  font-weight: 700;
}

.inner-item {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.3s ease;
}

.hero-tab-item:hover {
  background-color: #f2f2f2;
}

.hero-tab-item:hover>.inner-item>span>img {
  filter: none !important;
}

.inner-item>span>img {
  position: absolute;
  inset: 0px;
}

/* end of Hero Tab Navigation */

/* Hero Media */

@media (max-width: 767px) {

  .hero-cmp.large,
  .hero-cmp.normal {
    min-height: 576px;
  }

  .hero-cmp.small {
    min-height: 480px;
  }

  .hero-cmp.small.small-mobile {
    min-height: 280px;
  }

  .hero-cmp.small.small-mobile.contact .hero-item {
    padding-top: 70px;
  }

  .title-only-center .hero-item {
    margin-top: 0;
  }

  .hero-cmp.insurance .hero-item {
    padding-top: 337px;
  }

  .hero-cmp.large .hero-root {
    padding-bottom: 290px;
  }

  .hero-icon-arrow {
    margin-right: 16px;
    max-width: 44px;
    flex: 0 0 44px;
    height: 58px;
    align-items: center;
    margin-top: 34px;
  }

  .hero-icon-arrow>img {
    min-width: 100%;
  }

  .hero-button__url {
    padding-left: 0;
  }

  .hero-button__url a {
    max-width: unset;
    padding: 12px 16px;
  }

  .hero-content {
    padding: 337px 0 32px;
  }

  .hero-button__url.small-btn {
    padding-bottom: 32px;
  }

  .hero-button__url.small-btn a {
    padding: 12px 16px;
  }

  .mobile-padding-bottom-210 {
    padding-bottom: 210px;
  }

  .hero-item>h1 {
    padding-top: 0;
    margin-top: -8px;
    padding-left: 50px;
  }

  .hero-title-container>h1 {
    padding-top: 36px;
    white-space: normal;
  }

  .hero-description {
    margin-top: 20px;
    padding-left: 4px;
  }

  .hero-description>p {
    display: inline;
    vertical-align: top;
  }

  .loan.hero-cmp.large .hero-root {
    padding-bottom: 0;
  }

  .loan .hero-item {
    padding-top: 290px;
  }

  .loan .hero-description {
    margin-top: 16px;
  }

  .hero-tab-grid {
    padding: 0 12px;
    flex: 1 1;
    overflow: auto;
    border-radius: 0;
  }

  .hero-tab-grid::-webkit-scrollbar {
    display: none;
  }

  .hero-tab-item {
    min-width: inherit;
    flex-basis: 25%;
  }

  .hero-cmp .hero-wrapper-tab {
    width: 100%;
    padding-top: 0;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

@media (max-width: 991px) {
  .hero-wrapper-tab {
    padding: 0 48px;
  }

  .title-tab-center .hero-item {
    margin-top: 0;
  }

  .hero-cmp.normal.business-cmp {
    min-height: 400px;
  }

  .hero-cmp .hero-item.bussiness-item {
    margin-top: 236px;
  }

  .hero-item .hero-label {
    top: 300px;
  }

  .hero-title-container .hero-title.business-title {
    padding-top: 80px;
  }

  .hero-description.business-description {
    padding-top: 0;
  }

  .hero-button__url.business-button {
    padding-bottom: 36px;
    padding-top: 8px;
  }

  .hero-item .hero-description.pad-top-12>p {
    padding-top: 0;
  }
}

@media (max-width: 991px) {
  .hero-description.pad-top-12>p {
    padding-top: 12px;
  }
}

/* end of new Hero */

@media (max-width: 991px) {

  /* Footer Mobile */
  .footer-links__list {
    flex-wrap: wrap;
  }

  .footer-links__item {
    min-width: 50%;
  }

  /* End Footer mobile */
}

/* End of Image Slider Component*/

/* Table */
.table-container {
  padding: 48px 0;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
}

.table-container.four-column .table-cell-innercontent>div {
  min-width: 550px;
}

.table-container.four-column .table-cell-label {
  min-width: 240px;
}

.insurance-benefit p,
.cell-group-mobile {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5;
  color: #000;
}

.cell-group-mobile {
  display: none;
}

.table-content {
  width: 100%;
  display: block;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.table-content__title {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  position: relative;
  padding-bottom: 16px;
  font-family: SF Pro Display, SF Pro Display Italic, SF Pro Text, Helvetica Neue, Helvetica, Arial, sans-serif;
}

/* table no scroll */
.table-content.no-scroll .table-upper-title {
  padding: 35px 0;
}

.table-content.no-scroll .table-item-below {
  overflow-x: unset;
}

.table-content.no-scroll .table-cell-innercontent>div {
  min-width: unset;
  justify-content: unset;
}

.table-content.no-scroll .table-item-body .table-cell-content .table-cell-innercontent>div>div:first-child {
  flex: unset;
  width: 70%;
}

.table-content.no-scroll .table-cell-innercontent>div>div:last-child {
  flex: unset;
  width: 30%;
}

.table-content.no-scroll .table-body-row:first-child {
  background-color: #f5f6f8 !important;
}

.table-content.no-scroll .table-body-row:first-child .table-body-cell {
  padding: 24px 24px 8px;
}

.table-content.no-scroll .table-body-row:nth-child(2n) {
  background-color: #f5f6f8;
}

.table-content.no-scroll .table-body-row:nth-child(2n + 1) {
  background-color: #fff;
}

.table-content.no-scroll .table-body-cell:last-child {
  color: rgba(0, 0, 0, 0.87);
}

@media (max-width: 767px) {
  .table-content.no-scroll .table-body-row:first-child {
    display: none;
  }

  .table-content.no-scroll .table-body-row {
    display: flex;
    flex-direction: column;
    border-bottom: none;
    padding: 32px 24px;
  }

  .table-content.no-scroll .table-body-row * {
    font-size: 16px !important;
  }

  .table-content.no-scroll .table-cell-innercontent>div {
    display: flex;
    flex-direction: column;
  }

  .table-content.no-scroll .table-body-cell {
    width: 100%;
    border-bottom: none;
  }

  .table-content.no-scroll .table-cell-innercontent>div>div {
    margin: 0;
    width: 100% !important;
  }

  .table-content.no-scroll .table-body-root .table-body-row>td {
    padding: 0;
  }

  .table-content.no-scroll .table-cell-innercontent .moved-content {
    font-weight: 400 !important;
    font-style: italic;
    margin-bottom: 4px;
  }

  .table-content.no-scroll .table-body-cell:last-child {
    color: black;
  }
}

@media (max-width: 575px) {
  .table-content__title {
    padding-bottom: 8px;
  }
}

/* end of table no scroll */

.table-root {
  width: calc(100% + 24px);
  margin: -12px;
  justify-content: center;
  flex-wrap: wrap;
}

.table-grid {
  padding: 12px;
  flex-grow: 0;
  max-width: 100%;
  flex-basis: 100%;
}

.table-item-upper {
  display: flex;
  flex-direction: column;
  text-align: center;
  z-index: 3;
}

.background-top-table img {
  width: 100%;
  position: absolute;
  left: 0;
  top: -50%;
}

.table-upper-title {
  color: #fff;
  padding: 40px 0;
  z-index: 5;
}

.table-upper-title>label {
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.5;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.table-item-upper>span {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
  border-radius: 8px;
}

.table-item-upper>span img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: center center;
}

.table-item-below {
  box-shadow: unset;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.87);
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: #fff;
  z-index: 2;
}

.table-cell-innercontent a {
  color: blue;
  text-decoration: underline;
  font-weight: 400;
}

.table-item-below .table-cell-innercontent a {
  color: inherit;
  text-decoration: none;
}

.table-item-body {
  display: table;
  border-spacing: 0;
  border-collapse: collapse;
  min-width: 1167.28px;
}

.table-item-body.width-100 {
  width: 100%;
  min-width: 100%;
}

.table-body-root {
  display: table-row-group;
}

.table-body-row {
  display: table-row;
  outline: 0;
  vertical-align: middle;
  background-color: #f5f6f8;
  color: rgba(0, 0, 0, 0.87);
  border-bottom: 1px solid lightgray;
}

.table-body-row:nth-child(2n) {
  background-color: #fff;
}

.table-label-content i {
  font-weight: 400;
  color: rgba(0, 0, 0, 0.87);
}

.table-body-row .table-body-cell .font-weight-700 i {
  font-weight: 700px;
}

.table-label-content span.arrow {
  font-size: 25px;
  margin-left: 5px;
}

.table-label-content .detail {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.table-cell-innercontent>div>div>span {
  font-weight: 400;
}

div.table-label-content.business-label-content {
  font-size: 14px;
  text-transform: none;
}

.table-cell-content .table-cell-innercontent.business-table-innercontent div div p {
  font-size: 14px;
}

.table-item-body .table-cell-content .table-cell-innercontent.business-table-innercontent div div:nth-child(1) {
  font-weight: 400;
  font-size: 14px;
  width: 50%;
}

.table-item-body .table-cell-content .table-cell-innercontent.business-table-innercontent div div:nth-child(2) p {
  font-weight: 700;
}

.table-cell-innercontent>div>div>span.grey {
  color: var(--gray-600);
}

.table-body-cell {
  color: rgba(0, 0, 0, 0.87);
  display: table-cell;
  padding: 16px;
  font-size: 0.875rem;
  text-align: left;
  font-weight: 400;
  line-height: 1.43;
  border-bottom: 1px solid rgba(224, 224, 224, 1);
  letter-spacing: 0.01071em;
  vertical-align: inherit;
}

.table-body-cell:first-child {
  width: 25%;
}

.table-body-root .table-body-row>td {
  padding: 24px;
}

.table-label-content {
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.87);
  /* text-transform: uppercase; */
}

.insurance .table-label-content {
  text-transform: none;
}

.table-cell-innercontent {
  line-height: 1.5;
  font-size: 1rem;
  font-weight: 300;
}

.table-cell-innercontent div a {
  color: rgb(0, 176, 240) !important;
  text-decoration: underline !important;
}

.link_type.table-cell-innercontent {
  text-decoration: underline;
  color: blue;
  font-style: italic;
}

.table-cell-innercontent>div {
  display: flex;
  justify-content: space-between;
  min-width: 500px;
}

.insurance .table-cell-innercontent>div {
  min-width: 100%;
}

.table-cell-innercontent>div>div {
  margin: 0 24px 0 8px;
  flex: 1;
  letter-spacing: 0.01071em;
}

.table-cell-innercontent .cell-left {
  flex: 2;
}

.table-cell-innercontent>div>div p span {
  color: black;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
}

.table-cell-innercontent .column-wide {
  flex: 3;
}

.table-cell-innercontent>div>div>p {
  color: black;
  font-size: 16px;
}

.table-body-row:first-child .table-cell-innercontent>div>div>p {
  font-weight: 600;
}

.table-cell-innercontent[class*="title"]>div>div>p {
  font-size: 18px;
  font-weight: 600;
}

.table-cell-innercontent>div>div>ul {
  list-style: disc;
  color: rgb(97, 97, 97);
}

.table-container.list-disc .table-cell-innercontent>div>div>ul {
  list-style: disc;
  color: rgb(97, 97, 97);
}

.table-container.list-circle .table-cell-innercontent>div>div>ul {
  list-style: circle;
  color: rgb(97, 97, 97);
}

/* Table Media */
@media (min-width: 992px) {
  .table-container {
    padding: 64px 0;
  }

  .table-container__extend {
    padding: 88px 0;
  }
}

@media (min-width: 1200px) {
  .table-content {
    display: flex;
    flex-direction: column;
  }
}

@media (min-width: 1440px) {
  .table-content {
    max-width: 1440px;
  }
}

@media (max-width: 767px) {
  .insurance .table-cell-label {
    display: none;
  }

  .insurance tr.table-body-row:first-child {
    display: none;
  }

  .table-cell-innercontent>div>div {
    min-width: 144px;
  }

  .insurance .table-cell-innercontent div {
    display: flex;
    flex-direction: column;
  }

  .insurance-benefit::before {
    content: "Giá trị bảo vệ";
    font-weight: 400;
    font-style: italic;
    margin-bottom: 4px;
  }

  .cell-group-mobile {
    display: block;
  }
}

/* End of Table Media */
/* End of Table */

/* Card product banner */
.card-product-banner {
  min-height: 80px;
}

.card-product-banner .card-product-banner__link {
  display: flex;
  background-color: white;
  border-radius: 8px;
  justify-content: center;
  padding: 13px 34px 12px 18px;
}

.card-product-banner .card-product-banner__link:hover {
  text-decoration: underline;
}

.card-product-banner .card-product-banner__content {
  display: flex;
}

.card-product-banner .card-product-banner__img {
  height: 55px;
  width: 135px;
}

.card-product-banner .card-product-banner__text {
  display: flex;
  margin-left: 38px;
  flex-direction: column;
  justify-content: center;
}

.card-product-banner .card-product-banner__close {
  width: 18px;
  height: 18px;
  position: absolute;
  right: 28px;
  top: 10px;
  cursor: pointer;
}

.card-product-banner .card-product-banner__close:after {
  content: "";
  height: 18px;
  border-left: 2px solid #ed1c24;
  position: absolute;
  transform: rotate(45deg);
  left: 8px;
}

.card-product-banner .card-product-banner__close:before {
  content: "";
  height: 18px;
  border-left: 2px solid #ed1c24;
  position: absolute;
  transform: rotate(-45deg);
  left: 8px;
}

.card-product-banner .card-product-banner__text__title {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
}

.card-product-banner .card-product-banner__text__description {
  color: var(--gray-600);
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
}

.card-product-banner .card-product-banner__container {
  position: relative;
}

.card-product-banner__wrapper {
  position: relative;
}

@media (max-width: 768px) {
  .card-product-banner .card-product-banner__content {
    flex-direction: column;
  }

  .card-product-banner .card-product-banner__text {
    margin-top: 21px;
    margin-left: 0px;
  }
}

/* End card product banner */

/* List document download */

.list-document-download {
  min-height: 0;
}

.list-document-download__title {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  position: relative;
  padding-right: 24px;
  margin-right: 24px;
  margin-bottom: 18px;
}

.item-index {
  position: absolute;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #000;
  color: #fff;
  top: -50px;
  justify-content: center;
  border-radius: 50%;
  user-select: none;
}

.list-item-document-download__body {
  background-color: #fff;
  position: relative;
  width: 100%;
  padding: 40px 10px 24px;
  padding-left: 16px;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  flex-grow: 1;
  display: flex;
  flex-direction: row;
}

.list-item-document-download__container {
  padding-top: 30px;
  width: 100%;
  display: inline-block;
}

.item-document-download_content {
  max-width: none;
  margin: 0;
  display: block;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

.item-document-download_content .item-document-download_content-container {
  max-width: none;
  margin: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

.item-document-download_content .item-document-download_content-container>span {
  box-sizing: border-box;
  display: inline-block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: relative;
  max-width: 100%;
}

.item-document-download_content .item-document-download_content-container>span>img {
  display: block;
  width: 100%;
  height: 100%;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  border-radius: 8px;
}

.item-document-download_content a {
  line-height: 1.5;
  color: #000;
}

.item-link-document-text {
  font-weight: 600;
}

.item-document-download {
  margin-right: auto;
  flex: 1;
  padding: 0 10px;
}

.list-document-download .item-link-document {
  display: flex;
  align-items: center;
}

.item-img-content {
  display: inline-flex;
  margin-left: 0;
  transition: all 0.3s ease-in-out;
}

.item-document-download-dow {
  margin-left: 0.75em;
}

.list-document-download {
  overflow: hidden;
}

@media (max-width: 1334px) {
  .content-wrapper.list-document-download__container {
    position: relative;
  }

  .list-document-download__title {
    position: absolute;
  }

  .list-item-document-download__container {
    padding-top: 0;
  }

  .list-item-document-download__body {
    background-color: unset;
    padding: unset;
  }

  .list-item-document-download__body .item-document-download_content {
    background-color: white;
    padding: 40px 10px 24px 16px;
  }

  .item-document-download_content.radius-top-bottom-left-8 {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  .item-document-download_content.radius-top-bottom-right-8 {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .item-document-download .item-index {
    position: relative;
    top: 45px;
    margin-left: 16px;
  }

  .list-document-download .slick-list {
    padding-left: 24px;
  }

  .list-item-document-download__body .slick-arrow {
    position: absolute;
    border-radius: 50%;
    transition: all 0.2s ease-in-out;
    box-shadow: 1px 1.5px 4px 0 hsl(218deg 7% 69% / 45%);
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    background: hsla(0, 0%, 77%, 0.54);
    border: none;
    color: white;
    font-size: 1.25rem;
    z-index: 5;
  }

  .list-item-document-download__body .slick-arrow:hover {
    color: red;
    background-color: white;
  }

  .list-item-document-download__body .slick-prev::after {
    content: "❮";
  }

  .list-item-document-download__body .slick-next::after {
    content: "❯";
  }

  .list-item-document-download__body .slick-next {
    right: 0;
    top: 75%;
  }

  .list-item-document-download__body .slick-prev {
    left: 0;
    top: 75%;
  }
}

@media (max-width: 567px) {
  .list-item-document-download__body .item-document-download_content {
    min-height: 112px;
  }

  .list-document-download .slick-list {
    padding-left: 0px;
  }
}

/* End of List document download */

/* List Card Product */


/* End of List Card Product */

/* Card product feature*/


/*End Card product feature*/

/* Invest Potential component */
.invest-potential .list-tile__span img {
  z-index: -1;
}

.invest-potential {
  padding: 64px 0;
}

.invest-potential .list-tile__span img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: center center;
}

.invest-potential .mobile-img {
  display: none;
}

.invest-potential .invest-potential__container {
  display: flex;
  width: 100%;
  box-sizing: inherit;
  margin-left: auto;
  margin-right: auto;
  justify-content: center;
  flex-wrap: wrap;
}

.invest-potential .invest-potential__container-header {
  flex-grow: 0;
  max-width: 25%;
  flex-basis: 25%;
  padding-right: 24px;
}

.invest-potential .invest-potential__container-header h2 {
  margin-bottom: 20px;
  font-size: 28px;
  font-weight: 300;
  line-height: 35px;
}

.invest-potential .invest-potential__container-header ul {
  list-style: disc;
  padding-left: 24px;
}

.invest-potential .invest-potential__container-header li {
  margin-top: 8px;
}

.invest-potential .invest-potential__container-table {
  flex-grow: 0;
  max-width: 75%;
  flex-basis: 75%;
}

.invest-potential .container-table__title {
  background-color: #fff;
  padding: 24px 16px;
  border-radius: 8px 8px 0 0;
  text-align: center;
  color: #313131;
  font-weight: bolder;
}

.invest-potential .container-table__content {
  width: 100%;
}

.invest-potential .container-table__content-records {
  border-radius: 0 0 8px 8px;
  bottom: 0;
  overflow: auto;
}

.invest-potential .container-table__content-records table {
  min-width: 650px !important;
  width: 100%;
  border-collapse: collapse;
}

.invest-potential .table-row__header {
  background-color: #fff;
  border-bottom: 1px solid #dedede;
  border-top: 1px solid #dedede;
}

.invest-potential .table-row__header td {
  font-weight: 900;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 2px;
  text-align: center !important;
  padding: 8px 16px !important;
}

.invest-potential .table-row__header td:first-child {
  display: block !important;
  text-align: left !important;
  max-width: 150px;
}

.invest-potential .table-row__item {
  background-color: #fff;
}

.invest-potential .table-row__item:nth-child(2n) {
  background-color: #f5f6f8;
}

.invest-potential .table-row__item td {
  text-align: center !important;
  padding: 8px 16px !important;
  font-size: 16px;
  line-height: 24px;
  color: var(--gray-600);
}

.invest-potential .table-row__item td:first-child {
  justify-content: left;
  align-items: center;
  color: #000;
  font-weight: 600;
  display: block !important;
  text-align: left !important;
  max-width: 150px;
}

.invest-potential:not(.insurance) .table-row__item:last-child td p {
  color: rgb(0, 153, 255);
}

@media (max-width: 991px) {
  .invest-potential .invest-potential__container-header {
    max-width: 100%;
    flex-basis: 100%;
    padding-right: unset;
  }

  .invest-potential .invest-potential__container-table {
    max-width: 100%;
    flex-basis: 100%;
  }

  .invest-potential .container-table__title {
    margin-top: 44px;
  }
}

@media (max-width: 767px) {
  .invest-potential .list-tile__span {
    display: none;
  }

  .invest-potential .mobile-img {
    display: block;
  }
}

/* End Invest Potential component */



/* list-card-icon CSS */
/* .list-card-icon__container {
    background-color: rgb(255, 255, 255);
} */



/* Tab Vertical QA Component*/
.tab-vertical-qa__text-link {
  display: flex;
  align-items: center;
}

.tab-vertical-qa__title {
  font-size: 1.75rem;
  line-height: 1.25;
  font-weight: 300;
}

.tab-vertical-qa__button {
  display: flex;
  align-items: center;
  white-space: nowrap;
  justify-content: flex-start;
  position: relative;
  margin-left: 48px;
  padding: 16px 32px;
  border: 1px solid #000;
  border-radius: 8px;
  grid-gap: 12px;
  gap: 12px;
  min-width: max-content;
}

.tab-vertical-button--border-white {
  border: 1px solid var(--light-border);
}

.tab-vertical-qa__button::before {
  position: absolute;
  content: "";
  width: 1px;
  height: 29px;
  background-color: #c4c4c4;
  left: -24px;
  top: 50%;
  transform: translateY(-50%);
}

.tab-vertical-qa__button-text {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5;
}

a.tab-vertical-qa__button-text:hover {
  text-decoration: underline;
}

.tab-vertical-qa__button-icon {
  display: flex;
  align-items: center;
  transition: all 0.3s ease-in-out;
}

.tab-vertical-qa__tab {
  display: flex;
}

.tab-vertical-qa__tab-control {
  flex-grow: 0;
  max-width: 33.333333%;
  flex-basis: 33.333333%;
  padding: 36px 12px 12px 12px;
}

.tab-vertical-qa__tab-content {
  flex-grow: 0;
  max-width: 66.666667%;
  flex-basis: 66.666667%;
  padding: 12px;
  background-color: #fff;
}

.tab-active {
  background-color: #333;
  color: white;
}

.tab-vertical-qa__tab-control-btn {
  padding: 8px 16px;
  background-color: transparent;
  border-radius: 8px;
  color: #333;
  border: none;
  margin-top: 16px;
  font-size: 16px;
  line-height: 24px;
  width: 100%;
  text-transform: none;
  opacity: 1;
  min-height: 40px;
  letter-spacing: 0;
  max-width: none;
  text-align: left;
  cursor: pointer;
}

.tab-vertical-qa__tab-control-btn.tab-active {
  color: var(--primary-background);
  background-color: var(--gray-900);
}

.tab-vertical-qa__tab-control-btn img {
  display: none;
}

.tab-vertical-qa__article-content {
  padding: 48px 56px;
  border-radius: 8px;
}

.tab-vertical-qa__tab-content-title {
  margin: 0;
  color: #000;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
}

.tab-vertical-qa__tab-content-body {
  margin-top: 32px;
  color: #333;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  max-width: 92.72%;
}

.tab-vertical-qa__tab-content-video {
  margin-top: 24px;
}

.tab-vertical-qa__tab-content-video iframe {
  width: 100%;
  height: 424px;
  border-radius: 8px;
}

.tab-vertical-qa__modal-header {
  display: none;
}

@media (max-width: 1200px) {
  .tab-vertical-qa__tab-content-video iframe {
    height: 334px;
  }
}

@media (max-width: 992px) {
  .tab-vertical-qa__tab-content-video iframe {
    height: 271px;
  }
}

@media (max-width: 991px) {
  .tab-vertical-qa__tab {
    flex-direction: column;
  }

  .tab-vertical-qa__tab-control {
    max-width: 100%;
    flex-basis: 100%;
  }

  .tab-vertical-qa__text-link {
    flex-direction: column;
    align-items: flex-start;
  }

  .tab-vertical-qa__button::before {
    display: none;
  }

  .tab-vertical-qa__button {
    margin-left: 0;
    padding: 16px 32px;
    margin-top: 24px;
    width: 328px;
    justify-content: space-between;
  }

  .tab-active {
    color: #333;
  }

  .tab-vertical-qa__tab-control-btn {
    padding: 1rem 1.5rem;
    margin-top: 8px;
    display: flex;
    justify-content: space-between;
  }

  .tab-vertical-qa__tab-control-btn img {
    display: block;
    width: 16px;
    height: 16px;
  }

  .tab-vertical-qa__modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;
  }

  .tab-vertical-qa__modal-header span {
    padding: 1rem;
    margin: -1rem -1rem -1rem auto;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
    cursor: pointer;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    opacity: 0.5;
  }

  .tab-vertical-qa__article-content {
    background-color: white;
    padding: 1rem;
    position: fixed;
    flex-basis: 100%;
    z-index: 1;
    width: 100%;
    overflow: auto;
    max-width: 500px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
  }

  .tab-vertical-qa__modal-close {
    width: 24px;
    height: 24px;
    position: absolute;
    right: 16px;
    top: 16px;
  }

  .tab-vertical-qa__modal-close::before {
    content: "";
    height: 18px;
    border-left: 2px solid #a2a2a2;
    position: absolute;
    transform: rotate(-45deg);
    left: 8px;
  }

  .tab-vertical-qa__modal-close::after {
    content: "";
    height: 18px;
    border-left: 2px solid #a2a2a2;
    position: absolute;
    transform: rotate(45deg);
    left: 8px;
  }
}

@media (max-width: 767px) {
  .tab-vertical-qa__text-link {
    margin-bottom: 8px;
  }

  .tab-vertical-qa__tab-content-video iframe {
    height: 334px;
  }

  .tab-vertical-qa__button {
    width: 189.97px;
  }

  .about-us .tab-vertical-qa__button {
    width: auto;
    margin-top: 16px;
  }

  .about-us .tab-vertical-qa__text-link {
    margin-bottom: 12px;
  }
}

/* End of Tab Vertical QA Component*/
/* Business Information */
.business-information-container {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 32px;
  padding-bottom: 32px;
  background-color: rgb(255, 255, 255);
}

.businessinformation-main-container {
  width: 100%;
  padding: 0;
  position: relative;
  margin: 0 auto;
  max-width: 1440px !important;
}

.businessinformation-main-heading {
  font-weight: 300;
  font-size: 28px;
  line-height: 40px;
  color: #ed1c24;
}

.businessinformation-main-content {
  margin-top: 32px;
  margin-left: 0px;
}

.business-information-title {
  display: flex;
  justify-content: unset;
  align-items: center;
  gap: 35px;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5;
}

.business-information-title>h3 {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: var(--gray-600);
  margin-block-start: 0;
  margin-block-end: 0;
}

.business-information-content {
  justify-content: flex-end;
  flex-direction: row-reverse;
  padding-left: 52px;
  margin-top: 24px;
  display: flex;
  align-items: center;
}

.business-information-button {
  min-width: 393px;
  background-color: #ffffff;
  display: flex;
  padding-right: 24px;
  border-radius: 10px;
  border: 1px solid #000000;
}

.business-information-button>a {
  display: block;
  width: 100%;
  text-decoration: none;
  color: #000000;
  padding: 16px 0 16px 24px;
  border: none;
  outline: none;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
}

.business-information-button>img {
  width: 20px;
}

.businessinformation-main-content>span {
  position: relative;
  display: block;
  text-align: center;
  color: #a2a2a2;
  margin: 32px 0;
}

.businessinformation-main-content>span::before {
  content: "";
  display: block;
  width: calc(50% - 30px);
  height: 1px;
  top: 50%;
  right: 0;
  position: absolute;
  background-image: linear-gradient(to right, #a2a2a2 50%, rgba(255, 255, 255, 0) 0%);
  background-size: 10px 33px;
  background-repeat: repeat-x;
}

.businessinformation-main-content>span::after {
  content: "";
  display: block;
  width: calc(50% - 30px);
  height: 1px;
  top: 50%;
  position: absolute;
  background-image: linear-gradient(to right, #a2a2a2 50%, rgba(255, 255, 255, 0) 0%);
  background-size: 10px 33px;
  background-repeat: repeat-x;
}

.business-information-content>ul {
  max-width: 655px;
  list-style: disc;
}

.business-information-content>ul>li {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: var(--gray-600);
}

.business-information-spacebetween {
  justify-content: space-between;
}

/* Business Information Media */
@media (min-width: 992px) {
  .business-information-container {
    padding-top: 48px;
    padding-bottom: 48px;
  }
}

@media (min-width: 1200px) {}

@media (max-width: 768px) {
  .business-information-content {
    flex-direction: column;
    padding-left: 33px;
    margin-top: 8px;
  }

  .business-information-button {
    margin-top: 16px;
    margin-left: -33px;
    min-width: 356px;
    padding-right: 8px;
  }

  .businessinformation-main-content {
    margin-top: 12px;
    margin-left: 0;
  }

  .business-information-title {
    gap: 12px;
    align-items: flex-start;
  }

  .business-information-title>h3 {
    font-size: 18px;
  }

  .business-information-container>.business-information-wrapper {
    padding: 12px;
    padding-left: calc(100vw / 22.5) !important;
    padding-right: calc(100vw / 22.5) !important;
  }

  .businessinformation-main-heading {
    font-size: 24px;
  }

  .business-information-button>a {
    padding: 16px 0 16px 8px;
  }
}

/* End of Business Information Media */
/* End of Business Information */

/* Loan Options component */
.loan-option-component .loan-option-component__container {
  display: flex;
  flex-direction: column;
}

.loan-option-component__container .loan-options {
  display: flex;
  flex-direction: row;
}

.loan-option-component__container .title {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  padding-bottom: 12px;
  padding-left: 12px;
}

.loan-option-component__container .loan-options .image {
  margin-bottom: 16px;
}

.loan-option-component__container .loan-options .image h3 {
  margin-bottom: 8px;
}

.loan-option-component__container .loan-options .items {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px;
}

.loan-option-component__container .loan-options .image img {
  width: 120px;
  height: 100px;
}

.loan-option-component__container .loan-options .list-option {
  display: flex;
  flex-direction: column;
}

.loan-options .list-option .option {
  display: flex;
  flex-direction: row;
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  transition: 0.8s;
  justify-content: space-between;
}

.loan-options .list-option .option:hover {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}

.loan-options .list-option .option .content {
  padding: 0 12px;
}

.loan-options .list-option .option .content p:first-child {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 8px;
}

.loan-options .list-option .option .content p:last-child {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--gray-600);
}

@media (max-width: 767px) {
  .loan-option-component__container .loan-options {
    flex-direction: column;
  }
}

/* End of Loan Options */

/*Colunm information component*/
.colunm-information-section {
  background-color: rgb(255, 255, 255);
  display: block;
  margin: 0 auto;
  position: relative;
  max-width: 1920px;
}

@media (max-width: 767.98px) {
  .colunm-information-section.pad-wrapper {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

@media (min-width: 768px) {
  .colunm-information-section.pad-wrapper {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

@media (min-width: 992px) {
  .colunm-information-section.pad-wrapper {
    padding-top: 36px;
    padding-bottom: 36px;
  }
}

.colunm-information__container {
  justify-content: center;
  display: flex;
  box-sizing: border-box;
  font-family: SF Pro Display, SF Pro Display Italic, SF Pro Text, Helvetica Neue, Helvetica, Arial, sans-serif;
  flex-direction: row;
}

.colunm-information__title_field {
  padding: 12px;
  width: 33.333333%;
}

.colunm-information__item {
  flex-grow: 0;
  width: 16.666667%;
  flex-basis: 16.666667%;
}

.colunm-information__title_info {
  color: var(--gray-600);
  margin-top: 24px;
  font-size: 1rem;
}

.colunm-information__item {
  padding: 12px;
  margin: 0;
  box-sizing: border-box;
  flex-grow: 0;
}

.colunm-information__content {
  display: flex;
  border-radius: 8px;
  height: 100%;
  flex-direction: column;
  background-color: #f5f5f5;
  padding: 32px 16px;
  justify-content: stretch;
  align-items: center;
}

.colunm-information__content:hover {
  -webkit-box-shadow: 2px 3px 18px 0 hsl(0deg 6% 49% / 24%);
}

.colunm-information__item__header {
  width: 44px;
  position: relative;
}

.header_content_colunm {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: relative;
}

.colunm-information-img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: contain;
  object-position: center center;
  user-select: none;
}

.colunm-information__item__info {
  margin-top: 32px;
}

.colunm-information__item__info_title {
  margin-bottom: 8px;
}

.colunm-information__item__info_content {
  color: var(--gray-600);
  font-weight: 400;
  font-size: 1rem;
}

@media (max-width: 767.98px) {
  .colunm-information__container {
    justify-content: center;
    display: flex;
    box-sizing: border-box;
    flex-wrap: wrap;
    flex-direction: column;
  }

  .colunm-information__title_field {
    flex-grow: 0;
    width: 100%;
  }

  .colunm-information__item {
    width: 100%;
  }

  .colunm-information__item__info {
    margin-top: 0;
  }

  .colunm-information__content {
    display: flex;
    border-radius: 8px;
    height: 100%;
    flex-direction: column;
    background-color: #f5f5f5;
    padding: 32px 16px;
    justify-content: stretch;
    align-items: flex-start;
  }

  .colunm-information__title {
    font-weight: 300;
    line-height: 1.25;
    font-size: 1.65rem;
  }
}

.colunm-information__title {
  font-weight: 300;
  line-height: 1.25;
  font-size: 1.75rem;
}

/* End of Colunm info component*/




/* Tab Horizontal Report Component */
@media (max-width: 767.98px) {
  .tab-horizontal-report.pad-wrapper {
    padding-top: 32px;
    padding-bottom: 32px;
  }
}

@media (min-width: 768px) {
  .tab-horizontal-report.pad-wrapper {
    padding-top: 32px;
    padding-bottom: 32px;
  }

  .tab-horizontal-report .tab-horizontal-report__header-title.pad-right-24 {
    padding-right: 24px;
  }

  .tab-horizontal-report .tab-horizontal-report__header-link.pad-left-24 {
    padding-left: 24px;
  }
}

@media (min-width: 992px) {
  .tab-horizontal-report.pad-wrapper {
    padding-top: 48px;
    padding-bottom: 48px;
  }
}

.tab-horizontal-report .tab-horizontal-report__header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-bottom: 25px;
  flex-wrap: wrap;
}

.tab-horizontal-report .tab-horizontal-report__header-title {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  position: relative;
}

.tab-horizontal-report .tab-horizontal-report__header-title::before {
  display: block;
  position: absolute;
  content: "";
  width: 1px;
  height: 28px;
  background-color: #c4c4c4;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.tab-horizontal-report .tab-horizontal-report__header-link {
  justify-content: space-between;
  display: inline-flex;
  outline: none;
  border: none;
  cursor: pointer;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease-in;
  grid-gap: 12px;
  gap: 12px;
}

.tab-horizontal-report .tab-horizontal-report__header-link:hover>a {
  text-decoration: underline;
}

.tab-horizontal-report .tab-horizontal-report__tab-control {
  border-bottom: 1px solid #dedede;
  margin-bottom: 12px;
}

.tab-horizontal-report.tab-horizontal__fordevelopment .tab-horizontal-report__tab-control {
  display: none;
  height: 0;
  width: 0;
}

.tab-horizontal-report .tab-horizontal-report__tab-control-btn {
  margin-right: 22px;
  min-width: unset;
  font: 400 16px SF Pro Display;
  font-size: 16px;
  line-height: 24px;
  min-height: 40px;
  letter-spacing: 0;
  max-width: none;
  text-align: center;
  flex-shrink: 0;
  background-color: transparent;
  border-radius: 0;
  vertical-align: middle;
  border: none;
  color: #a2a2a2;
  cursor: pointer;
}

.tab-horizontal-report .tab-horizontal-report__tab-active {
  color: black;
  font-weight: bold;
  border-bottom: 4px solid #ed1c24;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.tab-horizontal-report .tab-horizontal-report__tab-content {
  margin-top: 72px;
  display: flex;
}

.tab-horizontal-report .tab-horizontal-report__tab-content-card {
  width: 304px;
  height: 100%;
  min-height: 350px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(65.49deg, transparent -4.44%, transparent 71.26%), #fff;
  border-radius: 8px;
}

.tab-horizontal-report.tab-horizontal__fordevelopment .tab-horizontal-report__tab-content-card {
  box-shadow: none;
}

.tab-horizontal-report.tab-horizontal__fordevelopment .tab-horizontal-report__header {
  padding-bottom: 0;
}

.tab-horizontal-report .tab-horizontal-report__tab-content-card.hidden {
  display: none;
}

.tab-horizontal-report .tab-horizontal-report__tab-card-img {
  margin-top: -24px;
  display: flex;
  justify-content: space-around;
}

.tab-horizontal-report .tab-horizontal-report__tab-card-img img {
  border-radius: 8px;
  width: 256px;
  height: 219px;
  object-fit: cover;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
}

.tab-horizontal-report .tab-horizontal-report__tab-card-content {
  padding: 0 24px;
  display: flex;
  flex-flow: column;
  flex: 1 1;
  margin-top: 20px;
}

.tab-horizontal-report .tab-horizontal-report__tab-card-content-title {
  line-height: 1.5;
  font-weight: 600;
  font-size: 1rem;
  align-items: center;
  transition: all 0.3s ease-in-out;
  color: #000;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.tab-horizontal-report .tab-horizontal-report__tab-card-content-description {
  color: #a2a2a2;
  flex-shrink: 0;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
}

.tab-horizontal-report .tab-horizontal-report__tab-card-action {
  margin: 16px 24px 32px;
}

.tab-horizontal-report .tab-horizontal-report__tab-card-action-link {
  justify-content: flex-start;
  display: inline-flex;
  outline: none;
  border: none;
  cursor: pointer;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease-in;
  grid-gap: 12px;
  gap: 12px;
}

@media (max-width: 1200px) {
  .tab-horizontal-report .tab-horizontal-report__tab-card-img img {
    width: 196px;
  }

  .tab-horizontal-report .tab-horizontal-report__tab-content-card {
    width: 244px;
  }
}

@media (max-width: 992px) {
  .tab-horizontal-report .tab-horizontal-report__tab-card-img img {
    width: 230px;
  }

  .tab-horizontal-report .tab-horizontal-report__tab-content-card {
    width: 278px;
  }
}

@media (max-width: 768px) {
  .tab-horizontal-report .tab-horizontal-report__tab-card-img img {
    width: 278px;
  }

  .tab-horizontal-report .tab-horizontal-report__tab-content-card {
    width: 326px;
  }
}

@media (max-width: 767px) {
  .tab-horizontal-report .tab-horizontal-report__header {
    flex-direction: column;
    align-items: flex-start;
  }

  .tab-horizontal-report .tab-horizontal-report__header-title::before {
    display: none;
  }

  .tab-horizontal-report .tab-horizontal-report__header-link {
    margin-top: 16px;
  }

  .tab-horizontal-report .tab-horizontal-report__header {
    padding-bottom: 20px;
  }
}

@media (max-width: 600px) {
  .tab-horizontal-report .tab-horizontal-report__tab-card-img img {
    width: 90%;
    object-fit: cover;
    object-position: center center;
  }

  .tab-horizontal-report .tab-horizontal-report__tab-content-card {
    width: 100%;
  }

  .tab-horizontal-report .tab-horizontal-report__tab-card-content-description {
    margin-top: 20px;
  }

  .tab-horizontal-report .tab-horizontal-report__tab-content {
    margin-top: 44px;
  }
}

/* End of Tab Horizontal Report Component */
/* Contact us*/
.contact-us__content__wrapper {
  display: flex;
  width: calc(100% + 24px);
  margin: -12px;
}

.contact-us__container {
  margin-top: 32px;
  margin-bottom: 32px;
}

.contact-us__title {
  padding-bottom: 12px;
}

.contact-us__title>div {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  padding-bottom: 8px;
}

.contact-us__item__wrapper {
  background-color: white;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #dedede;
}

.contact-us__item__wrapper:hover {
  box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
    0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
}

.contact-us__item__content__description {
  position: relative;
}

.contact-us__item__content__arrow {
  position: absolute;
  bottom: -3px;
  right: 0;
}

.contact-us__item__content__arrow img {
  height: 24px;
  width: 24px;
}

.contact-us__item__content__title p {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 8px;
  margin-top: 0;
}

.contact-us__item__content__description p {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--gray-600);
  margin: 0;
}

@media (min-width: 992px) {
  .contact-us__item {
    width: 33.333333%;
  }
}

@media (min-width: 768px) and (max-width: 992px) {
  .contact-us__content__wrapper {
    flex-wrap: wrap;
  }

  .contact-us__item {
    width: 50%;
  }
}

@media (max-width: 768px) {
  .contact-us__content__wrapper {
    flex-wrap: wrap;
  }

  .contact-us__item {
    width: 100%;
  }
}

/* End of Contact us*/

/* Support Component */
.support .support__container {
  padding: 64px 0;
}

.support .support__text {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: -4px;
  margin-bottom: 32px;
}

.support .support__title {
  padding: 4px;
  font-size: 1.75rem;
  font-weight: 300;
  line-height: 1.25;
}

.support .support__description {
  padding: 4px;
  color: var(--gray-600);
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
}

.support .support__list-item {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin: -12px;
}

.support .support__item {
  padding: 12px;
  flex-grow: 0;
  max-width: 33.333333%;
  flex-basis: 33.333333%;
}

.support .support__item-container {
  border-radius: 8px;
  padding: 24px 24px 0;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.87);
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: #fff;
}

.support .support__item-conent {
  margin: 32px 0;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.support .support__item-img {
  flex-grow: 0;
  max-width: 41.666667%;
  flex-basis: 41.666667%;
  height: 100px;
  position: relative;
}

.support .support__item-img img {
  object-fit: contain;
  object-position: left center;
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
}

.support .support__item-title {
  margin-top: 40px;
  margin-bottom: 8px;
  flex-grow: 0;
  max-width: 100%;
  flex-basis: 100%;
  font-size: 1.5rem;
  line-height: 1.5;
  font-weight: 600;
}

.support .support__item-description {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  height: 72px;
  flex-grow: 0;
  max-width: 100%;
  flex-basis: 100%;
}

.support .support__item-button {
  margin: 32px 0;
}

.support .support__item-link {
  background-color: transparent;
  border: 1px solid #000;
  justify-content: space-between;
  position: relative;
  display: inline-flex;
  padding: 16px 24px;
  border-radius: 8px;
  outline: none;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
  transition: all 0.3s ease-in;
  align-items: center;
  grid-gap: 12px;
  gap: 12px;
  width: 100%;
  font-weight: 600;
}

.support .support__item-link:hover {
  background-color: black;
  color: white;
}

@media (max-width: 991px) {
  .support .support__item {
    flex-grow: 0;
    max-width: 50%;
    flex-basis: 50%;
  }
}

@media (max-width: 767px) {
  .support .support__item {
    flex-grow: 0;
    max-width: 100%;
    flex-basis: 100%;
  }

  .support .support__container {
    padding: 48px 0;
  }

  .support .support__item-img {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
  }

  .support .support__item-title {
    margin-top: 24px;
  }

  .support .support__item-button {
    margin: 24px 0;
  }
}

/* End of Support Component */


/* Card List Component*/
.card-list .background-img picture {
  z-index: -1;
}

.card-list .card-list__title {
  padding-bottom: 32px;
  padding-right: 24px;
  margin-right: 24px;
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
}

.card-list .card-list__list-item {
  display: flex;
  width: 100%;
  gap: 24px;
}

.card-list .card-list__list-item.card-list__fordevelopment .card-list__item {
  max-width: 50%;
  flex-basis: 50%;
}

.card-list__fordevelopment .card-icon__fordevelopment {
  padding-top: 4px;
  padding-left: 12px;
}

.card-list .digital {
  min-height: 629.86px;
}

.card-list .digital.small {
  min-height: 0;
}

.card-list .card-list__item {
  max-width: 33.333333%;
  flex-basis: 33.333333%;
}

.card-list .width-50-50 .card-list__item {
  max-width: 50%;
  flex-basis: 50%;
}

.card-list .card-list__item-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 16%);
}

.card-list .card-list__item-container.less-shadow-hover {
  box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
    0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
}

.card-list .card-list__item-container:hover {
  box-shadow: 0 1px 4px rgb(0 0 0 / 50%);
}

.card-list .less-shadow-hover:hover {
  box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
    0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
}

.card-list .card-list__item-container:hover img {
  transform: scale(1.02);
}

.card-list .no-zoom:hover img {
  transform: none;
}

.card-list .card-list__item-img {
  position: relative;
  margin-bottom: auto;
  width: 100%;
  height: auto;
  overflow: hidden;
}

.card-list .digital .card-list__item-img {
  padding: 30px;
}

.card-list .digital.small .card-list__item-img {
  padding: 0 !important;
}

.card-list .card-list__item-img img {
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 100%;
  height: 100%;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: center center;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease-in-out;
  transform: scale(1);
  position: relative;
  transition: all 0.3s;
}

.card-list .card-list__item-body {
  padding: 24px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}

.card-list .card-list__fordevelopment .card-list__item-action {
  display: flex;
}

.card-list .card-list__fordevelopment .select-list__submit-icon {
  line-height: 24px;
}

.card-list .card-list__item-body.link-arrow .card-list__item-content {
  margin-bottom: 24px;
}

.card-list .digital .card-list__item-body {
  padding: 32px 33px 32px 24px;
}

.card-list .digital.small .card-list__item-body {
  width: 100%;
  padding: 24px 24px 32px;
}

.card-list .digital .card-list__item-body>p {
  color: var(--gray-600);
  font-size: 14px;
  letter-spacing: 2px;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 21px;
}

.card-list .card-list__item-content {
  font-size: 1.5rem;
  line-height: 1.5;
  margin-bottom: 8px;
}

.card-list .card-list__fordevelopment .card-list__item-content {
  color: var(--gray-900);
  font-weight: 300;
}

.card-list.exchange-rate .card-list__item-content {
  font-weight: 300;
}

.card-list .digital .card-list__item-content {
  margin-bottom: 0;
}

.card-list .digital.small .card-list__item-content {
  color: var(--gray-900);
  font-weight: 300;
  margin-bottom: 24px;
}

.card-list .card-list__item-content .card-list__item-description {
  margin-bottom: 24px;
}

.card-list .card-list__item-action {
  flex-shrink: 0;
  margin-top: auto;
}

.card-list .digital .card-list__item-action {
  margin-top: 0;
  color: var(--gray-600);
  letter-spacing: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.card-list .card-list__item-action-link {
  position: relative;
  display: inline-flex;
  outline: none;
  border: none;
  cursor: pointer;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease-in;
  width: inherit;
  grid-gap: 12px;
  gap: 12px;
  background-color: inherit;
  font-weight: 600;
}

.card-list__item-action-link span.material-symbols-outlined {
  color: #ed1c24;
  display: flex;
  align-self: flex-end;
}

a.card-list__item-body:hover .hover-underline {
  text-decoration: underline;
}

.card-list__item-action-link .hover-underline {
  color: var(--gray-600);
  font-weight: 300;
}

.card-list .card-list__item-action-link:hover {
  text-decoration: underline;
}

.card-list .card-list__item-content .card-list__item-financial-advice {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-bottom: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.card-list .card-list__item-content .card-list__item-financial-advice span:first-child {
  padding-right: 16px;
  margin-right: 16px;
  border-right: 1px solid #c5c5c5;
}

.card-list .card-list__item-content .card-list__item-title {
  font-weight: 600;
  font-size: 1rem;
}

.card-list .card-list__item-content .card-list__item-description {
  color: var(--gray-600);
  margin: 8px 0;
  font-size: 1rem;
}

.card-list.tools-and-utilities .card-list__item-content .card-list__item-description {
  color: #212121;
  font-size: 1.5rem;
  line-height: 1.5;
}

.display-webkit-box {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: 48px;
}

.flex-end-icon {
  display: flex;
  align-self: flex-end;
}

@media (max-width: 767px) {
  .card-list .card-list__item {
    max-width: 100%;
    flex-basis: 100%;
  }

  .card-list .card-list__container {
    overflow: hidden;
  }

  .card-list .slick-list {
    overflow: visible !important;
  }

  .card-list .card-list__list-item {
    display: unset;
    width: unset;
  }

  .card-list .digital {
    display: grid;
    overflow-x: scroll;
    grid-template-columns: repeat(3, 304px);
    grid-column-gap: 16px;
    padding-bottom: 64px;
  }

  .card-list .digital.small {
    padding-bottom: 8px;
  }

  .card-list .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .card-list .digital .card-list__item-img {
    padding: 16px;
  }

  .card-list .digital .card-list__item-body {
    padding-top: 4px;
    max-height: 50%;
  }

  .card-list .digital.small .card-list__item-body {
    max-height: unset;
    height: 50%;
  }

  .card-list .slick-dots {
    display: flex;
    justify-content: center;
    width: 100%;
    text-align: center;
    margin-top: 15px;
  }

  .card-list .slick-dots li {
    margin: 0 5px;
    width: 8px;
    height: 8px;
    cursor: unset;
    font-size: 0;
    line-height: 0;
    display: block;
    border: 0;
    outline: none;
    border-radius: 50%;
    background-color: #c4c4c4;
  }

  .card-list .slick-dots .slick-active {
    background-color: #ed1b24;
  }

  .card-list .slick-dots li button {
    display: none;
  }

  .card-list .slick-slide {
    padding: 8px;
  }

  .card-list .slick-track {
    margin: -8px;
  }
}

/* End of Card List Component*/
/* Card List Stack Component*/
.card-list-stack .card-list__item-content-title {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 8px;
}

.card-list-stack .card-list__item-content-description {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 16px;
}

@media (max-width: 767px) {
  .card-list-stack .card-list__item {
    max-width: 100%;
    flex-basis: 100%;
  }

  .card-list-stack .card-list__list-item {
    flex-direction: column;
  }

  .card-list-stack .card-list__list-item.mobile-scroll {
    flex-direction: row;
    overflow: auto;
  }

  .mobile-scroll .card-list__item {
    min-width: 100% !important;
  }

  .card-list-stack .card-list__item {
    padding: 0;
  }

  .card-list-stack .card-list__list-item {
    display: flex;
    margin: 0;
    grid-gap: 24px;
    gap: 24px;
  }
}

/* End of Card List Stack Component*/

/* End of Hero Product */

/* Priority text tile*/
.priority-text-tile__container {
  /* overflow: hidden; */
}

.priority-text-tile__bg {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.priority-text-tile__panel {
  display: flex;
  position: relative;
}

.priority-text-tile__button {
  text-decoration: none;
  justify-content: space-between;
  padding: 16px 24px;
  width: 322px;
  display: flex;
  border: 1px solid black;
  border-radius: 8px;
  background: linear-gradient(180deg, #8d8175 -24.11%, #35322b 305.36%);
}

.priority-text-tile__button-text {
  font-weight: bold;
  color: white;
}

.priority-text-tile__button:hover .priority-text-tile__button-text {
  color: var(--gray-600);
}

.priority-text-tile__button-icon {
  align-items: center;
  display: flex;
}

.priority-text-tile__button:hover {
  background: white;
}

.priority-text-tile__button:hover svg>path {
  fill: var(--gray-600);
}

.priority-text-tile__button svg>path {
  fill: white;
}

.priority-text-tile__information h2 {
  color: white;
  margin-top: 8px;
  line-height: 1.25;
  font-size: 1.75rem;
  font-weight: 300;
}

.priority-text-tile__information label {
  color: white;
}

.priority-text-tile__description {
  color: white;
  padding-top: 24px;
}

.priority-text-tile__button {
  margin-top: 32px;
}

.priority-text-tile__item__description {
  color: white;
  margin-top: 8px;
}

.priority-text-tile__item__title {
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.5;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.priority-text-tile__item {
  width: 100%;
  display: flex;
  height: 100%;
  position: relative;
  flex-direction: column;
  border-radius: 20px;
  justify-content: flex-end;
}

@media (min-width: 768px) {
  .priority-text-tile__container {
    padding-top: 48px;
    padding-bottom: 48px;
  }

  .priority-text-tile__panel {
    margin: -12px;
  }

  .priority-text-tile__content__wrapper {
    display: flex;
    align-items: center;
    padding: 12px;
    width: 50%;
  }

  .priority-text-tile__cardlist__wrapper {
    width: 50%;
    padding: 12px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    grid-auto-flow: column;
    grid-auto-rows: 1fr 1fr;
    grid-gap: 24px;
    gap: 24px;
  }

  .priority-text-tile__item__bg {
    object-fit: cover;
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 8px;
  }

  .priority-text-tile__item_wide {
    grid-column-end: span 2;
    aspect-ratio: 3;
  }

  .priority-text-tile__item__wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    width: calc(100% - 24px);
    padding-left: 16px;
    padding-bottom: 16px;
  }

  .priority-text-tile__item:hover {
    transform: scale(1.008);
  }

  .priority-text-tile__item {
    transition: all 0.3s ease;
    box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%),
      0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%),
      0 0.913195px 5.00873px rgb(0 0 0 / 1%);
  }
}

@media (max-width: 768px) {
  .priority-text-tile__container {
    padding-top: 32px;
    padding-bottom: 32px;
  }

  .priority-text-tile .content-wrapper {
    padding-left: calc(100vw / 22.5) !important;
    padding-right: calc(100vw / 22.5) !important;
  }

  .priority-text-tile__button {
    padding: 12px 16px;
  }

  .priority-text-tile__information {
    padding-left: 16px;
  }

  .priority-text-tile__item__bg {
    object-fit: cover;
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 15px;
  }

  .priority-text-tile__panel {
    flex-wrap: wrap;
    gap: 24px;
    width: calc(100% + 32px);
    margin-left: calc(100vw / (-22.5));
    margin-right: calc(100vw / (-22.5));
  }

  .priority-text-tile__content__wrapper,
  .priority-text-tile__cardlist__wrapper {
    width: 100%;
  }

  .priority-text-tile__container {
    display: flex;
  }

  .priority-text-tile__cardlist__wrapper {
    display: grid;
    grid-template-columns: unset;
    grid-auto-flow: column;
    grid-auto-columns: 90%;
    overflow-x: auto;
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
    scroll-snap-stop: always;
    padding-right: 16px;
    padding-left: 16px;
    scroll-padding: 16px;
    -ms-overflow-style: none;
    scrollbar-width: none;
    grid-gap: 16px;
    /* gap: 16px; */
  }

  .priority-text-tile__cardlist__wrapper::-webkit-scrollbar {
    display: none;
  }

  .priority-text-tile__button {
    width: 97%;
    border: none;
  }

  .priority-text-tile__item__wrapper {
    position: relative;
    bottom: 0;
    left: 0;
    width: calc(100% - 16px);
    padding-left: 16px;
    padding-bottom: 16px;
  }

  .priority-text-tile__item {
    aspect-ratio: 0.65;
    scroll-snap-align: start;
  }
}

/* End of priority text tile*/

/* Card List Center */

.card-center-container {
  min-height: 0;
}

.card-left-container {
  margin: 20px 0;
}

.card-center-container.small-card {
  min-height: unset;
}

.card-center-container.small-card .card-center-carditem>article {
  background: linear-gradient(181.48deg, #fff 47.95%, #ebebeb 121.09%);
}

.card-center-container.small-card .cardcenter-carditem-content div {
  text-align: center;
}

.card-center-container .large .cardcenter-carditem-content div ul {
  list-style: none;
  padding-left: 0;
}

.card-center-container .large .cardcenter-carditem-content div ul li {
  position: relative;
  padding-left: 24px;
  margin-left: 0;
}

.card-center-container .large .cardcenter-carditem-content div ul li::before {
  position: absolute;
  content: "";
  width: 5px;
  height: 5px;
  top: 10px;
  left: 0;
  background-color: #ed1b24;
  border-radius: 1px;
}

@media (max-width: 1920px) {
  .card-left-container {
    margin: 36px 0;
  }
}

@media (max-width: 991px) {

  .card-center-container .large .cardcenter-carditem-content div ul {
    margin-left: 0;
  }
}

@media (max-width: 767px) {
  .card-center-container.small-card .card-center-carditem>article {
    display: flex;
    flex-direction: column;
  }

  .card-center-wrapper {
    margin: 0;
  }

  .card-center-container .large .cardcenter-carditem-content div ul li::before {
    border-radius: 50%;
    background-color: var(--gray-600);
    left: 10px;
  }
}

.card-center-header {
  /* color: #000; */
  padding-bottom: 12px;
  margin: 0 auto;
}

.card-center-container .large .card-center-header {
  padding-bottom: 24px;
}

.cardcenter-header-titlecontain {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-bottom: 8px;
  flex-wrap: wrap;
}

.cardcenter-header-title {
  display: flex;
  align-items: center;
}

.cardcenter-header-title>div {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  position: relative;
  padding-right: 24px;
  margin-right: 24px;
}

.card-center-container .large .slick-list {
  margin: -12px;
  padding: 12px;
}

.card-center-container .slick-initialized .slick-track {
  display: flex;
}

.card-center-container .slick-initialized .slick-slide {
  height: auto;
}

.card-center-container .slick-initialized .slick-slide>div {
  height: 100%;
}

.card-center-container .large .slick-slide .card-center-carditem {
  height: 100%;
}

.card-center-container .large .slick-slide:not(:last-child) .card-center-carditem {
  padding-right: 24px;
}

.card-center-container .large .card-center-carditem>article {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 16%);
}

.card-center-container .large .card-center-carditem>article:hover {
  box-shadow: 0 0.0625em 0.0625em rgb(0 0 0 / 25%), 0 0.125em 0.5em rgb(0 0 0 / 25%),
    inset 0 0 0 1px hsl(0deg 0% 100% / 10%);
}

.card-center-container .large .cardcenter-carditem-header {
  height: 238.41px;
  margin: 0;
}

.card-center-container .large .cardcenter-carditem-headerimg {
  width: 100%;
}

.card-center-container .large .card-center-carditem:hover .cardcenter-carditem-headerimg {
  width: 100%;
  transform: scale(1.02);
}

.object-fit-contain {
  object-fit: contain;
}

.object-fit-cover {
  object-fit: cover;
}

.card-center-container .large .cardcenter-carditem-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 0;
  margin-left: 0;
  padding: 24px;
  height: 50%;
}

.card-center-container .large .cardcenter-carditem-content h4 {
  margin-bottom: 8px;
}

.font-weight-normal {
  font-weight: normal !important;
}

.font-weight-lighter {
  font-weight: lighter !important;
}

.card-center-container .large .cardcenter-carditem-content>div:nth-child(2) {
  font-weight: 600;
}

.card-center__bullet-list {
  list-style-type: disc;
  padding-left: 24px;
  margin-top: 8px;
}

.card-center-container .large .text-icon__nav-icon a {
  position: relative;
  display: inline-flex;
  outline: none;
  border: none;
  cursor: pointer;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease-in;
  justify-content: space-between;
  width: inherit;
  grid-gap: 12px;
  gap: 12px;
  background-color: inherit;
}

.card-center-container .large .slick-arrow {
  position: absolute;
  top: 48%;
  transform: translateY(-50%);
  z-index: 1;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
  box-shadow: 1px 1.5px 4px 0 hsl(218deg 7% 69% / 45%);
  display: flex !important;
  align-items: center;
  justify-content: center;
  background: hsla(0, 0%, 77%, 0.54);
}

.card-center-container .large .slick-arrow svg {
  color: #fff;
  transition: all 0.2s ease-in-out;
  font-size: 20px;
  fill: currentColor;
  width: 20px;
  height: 20px;
  display: inline-block;
  flex-shrink: 0;
  user-select: none;
}

.card-center-container .large .slick-prev {
  left: -27px;
}

.card-center-container .large .slick-prev svg {
  transform: rotate(180deg);
}

.card-center-container .large .slick-next {
  right: -24px;
}

.card-center-container .large .slick-disabled {
  display: none !important;
}

.card-center-container .large .slick-dots {
  margin-top: 28px;
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
}

.card-center-container .large .slick-dots li {
  color: #c4c4c4;
  width: 8px;
  font-size: 25px;
  margin: 0 5px;
}

.card-center-container .large .slick-dots>.slick-active {
  color: red !important;
}

.card-center-container .large .slick-dots button {
  display: none;
}

/*Article Card*/
.card-article-item-top {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
    0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
}

.card-article-item-topimg {
  height: 234px;
  max-width: 100%;
}

.card-article-item-topimg img {
  object-fit: cover;
  height: 100%;
  width: 100%;
}

.card-article-content {
  margin-top: 24px;
}

p.card-article-content-title {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 16px;
  font-weight: 600;
}

.card-article-content-des {
  color: var(--gray-600);
  font-size: 1rem;
  line-height: 1.5;
}

.card-article-content-des ul {
  list-style: none;
}

.card-article-content-des li {
  padding-left: 15px;
  position: relative;
}

.card-article-content-des li::before {
  position: absolute;
  content: "";
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: var(--gray-600);
  left: 0;
  top: 9px;
}

.dich-vu-thanh-toan .card-center-listcard {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-flow: row;
  grid-auto-rows: 1fr auto;
}

@media (max-width: 767px) {
  .cardcenter-header-title>div {
    padding-right: 0;
    margin-right: 0;
  }

  .card-center-container .large .card-center-header {
    padding-bottom: 0;
  }

  .dich-vu-thanh-toan .card-center-listcard {
    display: flex;
  }

  .card-center-container .large .card-center-carditem>article {
    flex-direction: column;
  }

  .card-center-container .large .cardcenter-carditem-header {
    height: 193.03px;
  }

  .card-center-container .no-slick-mobile {
    grid-gap: 24px;
    gap: 24px;
    grid-auto-rows: auto;
  }

  .card-center-container .no-slick-mobile .cardcenter-carditem-header {
    height: auto;
  }

  .card-center-container .no-slick-mobile .cardcenter-carditem-headerimg>span>span {
    padding: 57% 0px 0px;
  }

  .card-center-container .large .cardcenter-carditem-content {
    height: 284px;
    padding: 16px;
  }

  .card-center-container .large .no-slick-mobile .cardcenter-carditem-content {
    height: auto;
    padding: 16px;
  }

  .card-center-container .large .slick-list {
    margin: -8px;
    padding: 8px;
  }

  .card-center-container .large .slick-slide:not(:last-child) .card-center-carditem {
    padding-right: 0;
  }
}

@media (max-width: 575px) {
  .card-center-header {
    padding-bottom: 0;
  }
}

/* End of Card List Center Media */
/* End of Card List Center */



/* Tab Vertical Features component */
.tab-vertical-features-component {
  background-color: white;
  padding: 48px 0;
}

.tab-vertical-features-component .tab-vertical-features-component__container {
  display: flex;
  flex-direction: column;
}

.tab-vertical-features-component__container .title {
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  margin-bottom: 12px;
}

.tab-vertical-features-component__container .tab-content {
  display: flex;
  flex-direction: row;
  padding-top: 12px;
}

.tab-vertical-features-component__container .tab-content .menu {
  display: flex;
  flex-direction: column;
  width: 25.63%;
  margin-right: 112px;
}

.tab-vertical-features-component__container .tab-content .menu .menu-item {
  text-align: left;
  padding: 24px 0px;
  border: none;
  background-color: white;
  font-size: 16px;
  line-height: 24px;
  font-family: "SF Pro Display";
  min-width: 108px;
  color: rgb(97, 97, 97);
  min-height: 40px;
  border-bottom: 1px solid rgb(222, 222, 222);
  cursor: pointer;

  h3 {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }
}

.tab-vertical-features-component__container .tab-content .menu .menu-item:first-child {
  border-bottom: 4px solid rgb(237, 27, 36);
  color: black;
  font-weight: bolder;

  h3 {
    font-weight: 700;
  }
}

.tab-vertical-features-component__container .tab-content .list-items {
  flex: 1 1 0%;
}

.tab-vertical-features-component__container .tab-content .tab:nth-child(n + 2) {
  display: none;
}

.tab-vertical-features-component__container .tab-content .tab .item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-radius: 8px;
  height: 100%;
  width: 100%;
  cursor: pointer;
  align-items: center;
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(222, 222, 222);
  padding: 24px;
  gap: 16px;
  transition: 0.7s;
}

.tab-vertical-features-component__container .tab-content .tab .item:hover {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}

.tab-vertical-features-component__container .tab-content .tab .item .image {
  max-width: 50px;
  flex: 0 0 50px;
}

.tab-vertical-features-component__container .tab-content .tab .item .image img {
  width: 100%;
  height: 100%;
}

.tab-vertical-features-component__container .tab-content .tab .item .content h4 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 8px;
}

.tab-vertical-features-component__container .tab-content .tab .item .content {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1 1 0%;
}

.tab-vertical-features-component__container .tab-content .tab .item .content p {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: rgb(97, 97, 97);
}

.tab-vertical-features-component__container .tab-content .view-more {
  display: none;
}

@media (max-width: 1199px) {
  .tab-vertical-features-component__container .tab-content .menu {
    margin-right: 20px;
  }
}

@media (max-width: 991px) {
  .tab-vertical-features-component__container .tab-content {
    flex-direction: column;
  }

  .tab-vertical-features-component__container .tab-content .menu {
    flex-direction: row;
    margin-right: 0;
    width: 100%;
    align-items: center;
    overflow: visible;
    overflow-x: scroll;
    flex: 1 1 auto;
    white-space: nowrap;
    justify-content: flex-start;
    margin-bottom: 24px;
    border-bottom: 1px solid rgb(222, 222, 222);
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .tab-vertical-features-component__container .tab-content .menu::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .tab-vertical-features-component__container .tab-content .menu {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .tab-vertical-features-component__container .tab-content .menu .menu-item {
    margin-right: 24px;
    min-width: unset;
    border-bottom: none;
    padding: unset;
  }

  .tab-vertical-features-component__container .tab-content .tab {
    display: flex;
    flex-direction: column;
    row-gap: 16px;
  }

  .tab-vertical-features-component__container .tab-content .view-more {
    display: flex;
    margin-top: 24px;
    text-align: left;
    cursor: pointer;
  }

  .tab-vertical-features-component__container .tab-content .view-more:hover span {
    text-decoration: underline;
  }

  .tab-vertical-features-component__container .tab-content .view-more span {
    font-weight: 600;
    font-size: 1rem;
  }

  .tab-vertical-features-component__container .tab-content .view-more i {
    margin-left: 12px;
    color: rgb(237, 27, 36);
  }

  .tab-vertical-features-component__container .tab-content .menu .menu-item {
    overflow: unset !important;
  }

  .tab-vertical-features-component__container .tab-content .menu .menu-item:hover {
    background-color: #f5f6f8;
  }
}

.tab-vertical-features-component__container .tab-content .menu .menu-item {
  position: relative;
  display: block;
  overflow: hidden;
}

.ripple-btn-tab-vertical {
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(128, 128, 128, 0.575);
  transform: scale(0);
  position: absolute;
  opacity: 1;
}

.ripple-animation-tab-vertical {
  animation: rippleTabVertical 0.6s linear;
}

@keyframes rippleTabVertical {
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* End of Tab Vertical Features component */

/* Reuse margin top/bottom for section */
.section__margin-small {
  margin-top: 36px;
  margin-bottom: 36px;
}

.section__padding-small {
  padding-top: 36px;
  padding-bottom: 36px;
}

.section__margin-medium {
  margin-top: 48px;
  margin-bottom: 48px;
}

.section__margin-large {
  margin-top: 64px;
  margin-bottom: 64px;
}

.section__padding-medium {
  padding-top: 48px;
  padding-bottom: 48px;
}

.section__padding-large {
  padding-top: 64px;
  padding-bottom: 64px;
}

.no-padding-bottom {
  padding-bottom: 0 !important;
}

.no-padding-top {
  padding-top: 0 !important;
}

@media (max-width: 767px) {
  .section__padding-large {
    padding-top: 48px;
    padding-bottom: 48px;
  }
}

@media (max-width: 991px) {
  .section__margin-small {
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .section__padding-small {
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .section__margin-medium {
    padding: 0;
    margin-top: 32px;
    margin-bottom: 32px;
  }

  .section__padding-medium {
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 32px;
    padding-bottom: 32px;
  }
}

/* End Reuse margin top/bottom for section */

/* Condition components */
.conditions-inner-wrapper {
  max-width: 100%;
  padding-top: 32px;
  overflow-x: auto;
}

.conditions-content {
  display: flex;
  background: #fff;
  border-radius: 8px;
  padding: 0 10px;
  position: relative;
  min-width: 534px;
}

.condition-item {
  padding: 40px 10px 24px;
  position: relative;
  flex: 1;
}

h3.condition-index {
  position: absolute;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #000;
  color: #fff;
  top: -45px;
  justify-content: center;
  border-radius: 50%;
}

/* Priority Page CSS */
.header_layout .priority__header-primary {
  background-color: #333;
}

.header_layout .priority__menu a {
  max-width: 215px;
}

.header_layout .priority__header-primary .link_component-link:hover,
.header_layout .priority__header-primary .link_component-link.active,
.header_layout .priority__header-primary .language_item .link_component-link {
  color: #ecd7b0;
}

.header_layout .navigation-primary_left .navigation-primary_item.priority-active {
  font-weight: 700;
  color: #ecd7b0;
}

.header_layout .priority__header-primary .language_item .material-symbols-outlined {
  color: #a2a2a2;
}

.navigation-primary_left .navigation-primary_item.priority-active::before {
  bottom: -16px;
}

.header-navigation .navigation-primary_item.priority-active::before {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  content: "";
  border-right: 16px solid transparent;
  border-left: 16px solid transparent;
  border-bottom: 16px solid #000;
}

.header_layout .priority__logo img {
  height: 32px;
}

.header_layout .priority__background-color {
  background-color: #000;
}

.priority__background-color {
  background-color: #000;
}

.priority__background-color .button-link__icon-svg {
  display: flex;
}

.header_layout .priority__font-color {
  color: #fff;
  padding-right: 24px;
  margin-right: 24px;
}

.section-wrapper .priority__font-color p {
  padding-right: 24px;
  margin-right: 24px;
}

.section-wrapper .priority__font-color p::before {
  display: block;
  position: absolute;
  content: "";
  width: 1px;
  height: 28px;
  background-color: #c4c4c4;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.section-wrapper .priority__title-link a {
  display: inline-flex;
  justify-content: space-between;
  grid-gap: 12px;
  gap: 12px;
  align-items: center;
}

.section-wrapper .priority__title-link .title-contain__button-link {
  font-size: 16px;
  font-weight: 600;
  transition: 0.5s;
  z-index: 5;
}

.section-wrapper .priority__title-link:hover .title-contain__button-link {
  text-decoration: underline;
  cursor: pointer;
}

.section-wrapper .priority__tile-item .list-tile__tile-item {
  min-height: 500px;
}

.section-wrapper .priority__tile-item .list-tile__tile-item.priority-overview {
  max-height: 500px;
}

.section-wrapper .priority__navtext {
  position: relative;
}

.section-wrapper .priority__navtext span {
  margin-left: 0;
  grid-gap: 12px;
  gap: 12px;
}

.priority__font-color {
  color: #fff;
}

@media (max-width: 767px) {
  .header_layout .priority__header-logo {
    max-width: 80px;
    padding: 14px 0;
  }

  .priority__hero-small {
    min-height: 280px;
  }

  .hero-small .priority__hero-image img {
    margin: 0;
    padding: 0;
  }

  .hero-small .priority__hero-wrapper {
    height: 280px;
  }

  .section-wrapper .priority__font-color p {
    padding-right: 0;
    margin-right: 0;
  }

  .section-wrapper .priority__title-link {
    margin-top: 16px;
  }

  .section-wrapper .priority__font-color p::before {
    display: none;
  }

  .section-wrapper .priority__tile-item {
    display: flex;
  }

  .priority-small {
    min-height: 280px !important;
  }

  .section-wrapper .priority__tile-item .list-tile__tile-item {
    max-height: none !important;
  }
}

@media (max-width: 991px) {
  .section-wrapper .priority__tile-item .list-tile__tile-item.priority-overview {
    max-height: 500px;
    min-height: auto;
  }

  .header_layout .priority {
    background: #333;
    height: calc(100% - 76px);
  }

  .header_layout .priority .mobile-button,
  .header_layout .priority .mobile-expand {
    border: none;
    background: linear-gradient(180deg, #8d8175 -42.86%, #35322b 234.82%);
  }

  .header_layout .priority .link_text.active {
    color: #ecd7b0;
    letter-spacing: 2px;
  }

  .header_layout .priority .header-text.align-center,
  .header_layout .priority .priority__font-color,
  .header_layout .priority .mobile-expand .header-text,
  .header_layout .priority .mobile-expand .navigation-primary_left .navigation-primary_item .link_component-link {
    color: white;
  }

  .header_layout .priority .navigation-primary_item .link_component-link:hover {
    color: var(--gray-600);
    text-decoration: underline;
  }

  .header_layout .priority .mobile-menu-items .language_item .link_component-link:hover {
    color: red;
    text-decoration: none;
  }

  .header_layout .priority .navigation-primary_item .material-symbols-outlined.gray_text {
    color: #a2a2a2;
  }

  .header_layout .priority .navigation-primary_item.language_item .link_component-link:not(.gray_text) .link_text,
  .header_layout .navigation-primary_right .navigation-primary_item.language_item .link_component-link:not(:last-child),
  .header_layout .priority .mobile-expand .navigation-primary_left .navigation-primary_item.priority-active .link_component-link {
    color: #ecd7b0;
  }

  .header_layout .priority .mobile-expand .navigation-primary_left .navigation-primary_item .link_component-link:hover,
  .header_layout .priority__background-color .navigation-secondary_actions .link_text:hover {
    text-decoration: none;
  }
}

/* End Priority Page CSS */

/* Section Background color */
.no-background {
  background-color: unset;
}

/* End Section Background color */

/* text image vertical component */
.text-image-vertical-component .content-container__title.small-text h2 {
  font-weight: 400;
  font-size: 1rem;
  color: hsl(0, 0%, 0%);
}

.tcb-sections-container {
  position: relative;
}

.tcb-sections-container .techcombank-mobile-bg-img>span {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
}

.tcb-sections-container .techcombank-mobile-bg-img>span>img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: center center;
}

/* Component Logo List */
.title-cmp {
  padding-bottom: 12px;
}


.title-cmp__title {
  padding-bottom: 8px;
  font-weight: 300;
  font-size: 1.75rem;
  line-height: 1.25;
  position: relative;
  font-size: 32px;
}

.title-cmp__description {
  color: var(--gray-600);
  margin-bottom: 12px;
  position: relative;
}

.title-cmp__btn-link {
  color: var(--gray-600);
  margin-bottom: 12px;
  position: relative;
}

.title-cmp__subtitle {
  font-size: 1.5rem;
  line-height: 36px;
  font-weight: 300;
}

.title-cmp__link {
  position: relative;
  display: inline-flex;
  outline: none;
  border: none;
  cursor: pointer;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease-in;
  justify-content: center;
  width: inherit;
  grid-gap: 12px;
  gap: 12px;
  background-color: inherit;
}

.logo-list .logo-list__logo-container {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.logo-list .logo-list__list-item {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  justify-content: center;
  width: 100%;
  margin: -8px;
}

.logo-list .logo-list__item {
  flex-grow: 0;
  max-width: 16.666667%;
  flex-basis: 16.666667%;
  padding: 8px;
}

.logo-list .logo-list__item-container {
  border-radius: 8px;
  height: 110px;
  position: relative;
  background-color: #fff;
  box-shadow: 0 0.0625em 0.0625em rgb(0 0 0 / 25%), 0 0.125em 0.5em rgb(0 0 0 / 25%),
    inset 0 0 0 1px hsl(0deg 0% 100% / 10%);
}

.logo-list .logo-list__item-container>span {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
}

.logo-list .logo-list__item-container>span>img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: contain;
  object-position: center center;
}

@media (max-width: 767px) {
  .logo-list .logo-list__list-item {
    overflow: scroll;
    justify-content: space-between;
    flex-wrap: nowrap;
  }

  .logo-list .logo-list__item-container {
    width: 140px;
  }

  .logo-list .logo-list__item {
    flex-grow: unset;
    max-width: unset;
    flex-basis: unset;
  }
}

@media (max-width: 575px) {
  .title-cmp {
    padding-bottom: 0;
  }
}

/* End of Component Logo List */

/* Component Floating Banner */
.floating-banner {
  top: 0px;
  transition: all 0.3s ease 0s;
}

.floating-banner.bg-gray {
  background-color: rgb(111, 108, 108);
}

.floating-banner .floating-banner__container {
  padding: 16px 0;
}

.floating-banner .floating-banner__container>span {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
}

.floating-banner .floating-banner__container>span>img {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: center center;
}

.floating-banner .floating-banner__content {
  display: flex;
  justify-content: unset;
  align-items: flex-start;
  position: relative;
  grid-gap: 32px;
  gap: 32px;
}

.floating-banner .floating-banner__content .floating-banner__btn-close {
  position: absolute;
  top: 4px;
  right: 0;
  cursor: pointer;
  max-width: 24px;
  max-height: 24px;
}

.floating-banner .floating-banner__announce-label {
  display: flex;
  align-items: center;
  grid-gap: 8px;
  gap: 8px;
  flex-shrink: 0;
}

.floating-banner .floating-banner__announce-label>.floating-banner__announce-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
}

.floating-banner .floating-banner__announce-label>.floating-banner__announce-text {
  font-size: 16px;
  line-height: 24px;
}

.floating-banner .floating-banner__announce-label>.floating-banner__announce-text>p {
  font-weight: 600;
}

.floating-banner .floating-banner__description {
  padding-right: 24px;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
}

.floating-banner.floating-banner__bg-gray {
  background-color: rgb(111, 108, 108);
}

.floating-banner .floating-banner__bg-img-desktop {
  display: block;
}

.floating-banner .floating-banner__bg-img-mobile {
  display: none;
}

/* End of Component Floating Banner */

/* Component Floating Toolbar */
.floating-toolbar {
  width: auto;
  height: auto;
  position: fixed;
  top: 50%;
  right: 0;
  z-index: 1299;
}

.floating-toolbar .floating-toolbar__icon {
  background-color: rgb(255, 255, 255);
  display: flex;
  justify-content: unset;
  align-items: center;
  grid-gap: 24px;
  gap: 24px;
  padding: 16px 15px;
  background: #fcfcfc;
  border: 1px solid rgba(0, 0, 0, 0.01);
  border-radius: 6px 0 0 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 56px;
  min-width: 56px;
  pointer-events: unset;
}

.floating-toolbar .floating-toolbar__icon-content {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  display: none;
}

.floating-toolbar .floating-toolbar__list-icon:hover .floating-toolbar__icon-content {
  display: block;
}

.floating-toolbar .floating-toolbar__icon:hover {
  background-color: rgb(255, 0, 0);
}

.floating-toolbar .floating-toolbar__icon:hover .floating-toolbar__icon-content {
  color: rgb(255, 255, 255);
}

.floating-toolbar .floating-toolbar__icon:hover>svg>path {
  fill: rgb(255, 255, 255);
}

.floating-toolbar .floating-toolbar__icon:hover>svg>rect {
  fill: rgb(255, 255, 255);
}

/* End of Component Floating Toolbar */

/* Component Scroll to top */
.scroll-to-top {
  width: 40px;
  height: 40px;
  position: fixed;
  top: calc(100% - 140px);
  right: 56px;
  z-index: 15;
  display: none;
  border-radius: 50%;
  overflow: hidden;
  opacity: 0;
  transform: translate3d(0, 100px, 0);
  transition: all 0.3s ease-in-out;
}

.scroll-to-top.show {
  // Change from Adobe
  display: block;
  opacity: 0.3;
  transform: translate3d(0, 0, 0);
  transition: all 0.3s ease-in-out;
}

.scroll-to-top.show:hover {
  opacity: 1;
  .scroll-to-top__icon {
    background-color: var(--primary-red);
  }

}

.scroll-to-top .scroll-to-top__icon {
  width: 100%;
  height: 100%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s linear;
  background-color: rgb(217, 217, 217);
  opacity: 1;
}

.scroll-to-top>svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s linear;
  cursor: pointer;
}


.scroll-to-top:hover svg {
  filter: brightness(0) invert(1);
}

/* End of Component Scroll to top */
/* End of Component CSS */

.content-wrapper {
  max-width: 1440px;
  padding-left: 64px;
  padding-right: 64px;
  margin: 0 auto;


  @media (max-width: 1199px) {
    padding-left: calc(100% / 22.5);
    padding-right: calc(100% / 22.5);

    .imagecardslide {
      padding: 0;
    }
  }

  @media (max-width: 992px) {
    padding-left: calc(100vw / 22.5);
    padding-right: calc(100vw / 22.5);
  }

  @media (max-width: 768px) {
    padding-left: calc(100vw / 22.5);
    padding-right: calc(100vw / 22.5);
  }

  @media (max-width: 390px) {
    padding-left: 17px;
    padding-right: 17px;
  }
}

@media (max-width: 768px) {

  // Change from Adobe
  .header-navigation .navigation_primary-wrapper.content-wrapper {
    padding: 36.44px;
  }
}

@media (max-width: 390px) {

  .header_layout .header-navigation .navigation_secondary-wrapper .navigation-secondary_menu,
  .header_layout .header-navigation .navigation_primary-wrapper {
    width: calc(100% - 34px);
  }
}

/** Hero Slider **/
.hero-slider {
  position: relative;
}

.hero-slider_slide {
  position: relative;
}

.hero-slider_background {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 40px;
  left: 0;
}

.hero-slider_background img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.hero-slider_content {
  display: flex;
  align-items: stretch;
  justify-content: flex-end;
  position: relative;
  z-index: 1;
}

.hero-slider_content-body {
  background: var(--gray-900);
  color: var(--primary-background);
  padding: 40px;
  width: 528px;
  min-height: 560px;
  max-width: 40%;
  border-radius: 0 0 8px 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tcb-grid-card_header,
.tcb-section_header,
.news-list_header,
.stock-information_column-header,
.awards_header,
.hero-slider_slide-header {
  font-size: 24px;
  font-weight: 300;
  position: relative;
}

.tcb-section_header--dark {
  color: var(--primary-background);
}

.hero-slider_slide-header::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 24px;
  height: 24px;
  left: -52px;
  top: 50%;
  margin-top: -12px;
  transform: rotate(45deg);
  background: var(--accent);
}

.hero-slider_slide-subheader {
  font-size: 14px;
  letter-spacing: 2px;
  font-weight: 600;
  text-transform: uppercase;
}

.hero-slider_slide-description {
  flex: 1;
}

.hero-slider_indicator {
  z-index: 1;
  position: absolute;
  bottom: 80px;
  left: 60px;
  display: flex;
  flex-direction: column;
  color: var(--primary-background);
  gap: 8px;
  align-items: center;
  font-size: 10px;
}

.hero-slider_dot {
  display: inline-flex;
  position: relative;
  background: #a2a2a2;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.hero-slider_dot.hero-slider_dot--active {
  margin-top: 48px;
  align-items: flex-start;
  justify-content: flex-start;
}

.hero-slider_dot.hero-slider_dot--active::after,
.hero-slider_dot.hero-slider_dot--active::before {
  content: "";
  display: inline-block;
  position: relative;
  height: 40px;
  width: 2px;
  top: -48px;
  left: 3px;
}

.hero-slider_dot.hero-slider_dot--active::before {
  background: var(--primary-background);
}

.hero-slider_dot.hero-slider_dot--active::after {
  background: var(--accent);
  z-index: 1;
  margin-left: -2px;
  animation: hero-progress 5s linear;
}

.card-note li {
  margin-top: 6px;
  line-height: 24px;
}

.hero-slider_current-slide {
  color: var(--accent);
}

@keyframes hero-progress {
  from {
    height: 0;
  }

  to {
    height: 40px;
  }
}

/** End of hero Slider **/

/* Card List Grid */

/* End of Card List Grid */

/* CSS for award */
.tcb-tab-content {
  margin-top: 24px;
}

.tab-item_list.tcb-tab-content[data-tab-style="image-slider"] {
  height: 722px;
}

.tcb-tab-content_container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
}

.tcb-tab-content_container.image-slider {
  overflow: visible;
}

.tcb-tab-content_container[data-display="block"] {
  display: block;
}

.tcb-card,
.news-card,
.tab-item {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
}

.tab-item.image-slider__container {
  background: none;
}

.tab-item .image-slider__list-item {
  position: absolute;
  max-width: 100%;
}

.awards_header {
  margin-bottom: 20px;
}

.tcb-card_cover,
.news-card_cover,
.award-card_cover {
  width: 100%;
}

.image-slider .tab-item.hidden {
  display: block;
  opacity: 0;
  z-index: -1;
}

.tcb-card_cover-image,
.news-card_cover-image,
.award-card_cover-image {
  width: 100%;
  max-height: 400px;
  aspect-ratio: 1.77;
  object-fit: cover;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.tcb-card_info,
.news-card_info,
.award-card_info {
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tcb-card,
.news-card_title,
.award-card_title {
  color: var(--body);
  letter-spacing: -0.01em;
  font-weight: 600;
}

.news-card_type-inner,
.award-card_year-inner {
  font-size: 14px;
  line-height: 16px;
  letter-spacing: 2px;
  color: var(--accent);
  text-transform: uppercase;
  padding-right: 16px;
  border-right: 1px solid var(--light-border);
  font-weight: 600;
}

.award-card_provider {
  color: var(--light-secondary-text);
  letter-spacing: -0.005em;
}

.tcb-card_description {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 400;
}

.news-card_description {
  color: var(--gray-600);
}

.news-card_description ul {
  padding-left: 20px;
}

.news-card_time {
  font-size: 14px;
  line-height: 16px;
  letter-spacing: 2px;
  margin-left: 16px;
  color: var(--gray-600);
  text-transform: uppercase;
  font-weight: 600;
}

.news-card_month {
  position: absolute;
  top: 24px;
  right: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 20px;
  background: var(--primary-background);
  box-shadow: 0 33px 181px rgb(0 0 0 / 4%);
  border-radius: 8px;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 24px;
  line-height: 36px;
  letter-spacing: 2px;
}


.news-card_month small {
  font-weight: 600;
  font-size: 12px;
  line-height: 14px;
  letter-spacing: 1px;
  text-align: center;
}

/* End css for award */

/** More Info **/
.more-info {
  position: relative;
  overflow: hidden;
  padding: 48px 0;
}

.more-info .tcb-section_header {
  margin: 0;
  flex-shrink: 0;
}

.more-info_background_image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.more-info_body .tcb-scroll-controls {
  display: none;
}

.more-info_body {
  display: flex;
  position: relative;
  z-index: 1;
  gap: 24px;
  align-items: center;
}

.more-info_card-list {
  display: flex;
  position: relative;
}

.more-info_card-scroller {
  display: flex;
  gap: 24px;
  flex-wrap: nowrap;
}

.more-info_card {
  flex: 1;
  transition: all 0.3s ease 0s;
}

.more-info_background-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

/** End of more Info **/

/* Checkbox Info Table Component */
.checkbox-info-table-component .select-checkbox-filter {
  padding-bottom: 24px;
  align-items: center;
}

.checkbox-info-table-component .tab .row.showed {
  display: flex;
}

/* End of Checkbox Info Table Component */

/* Select Info Table Component */
.select-info-table-component .select-filter {
  padding: 0 0 24px 0;
}

.select-info-table-component .view-more .btn {
  border: 1px solid #000;
}

.select-info-table-component .tab .row.showed {
  display: flex;
}

/* End of Select Info Table Component */

/* Press Article */
.article-header {
  text-align: center;
}

.press-article .article-header__title,
.press-article .article-header__subTitle,
.press-article .article-header__date {
  margin: 8px 0 0;
}

.press-article .article-header__subTitle {
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.25;
  color: #8d8175;
}

.press-article .article-header__date {
  font-weight: 400;
  font-size: 0.875rem;
  font-style: Italic;
  color: #a2a2a2;
}

/* End of Press Article */

@keyframes fadeIn {

  0%,
  50% {
    opacity: 0;
  }

  100% {
    opacity: 0.4;
  }
}

/* inspire.html */
.tcb-modal.inspire-register-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}

.inspire-register-modal .tcb-modal_wrapper {
  max-width: 1200px;
  width: 100%;
  display: flex;
}

.inspire-register-modal .inspire-left-col {
  padding: 60px 48px;
}

/* header navigation style */

.header_list_dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  font-size: 16px;
  background-color: #fff;
  transform: scaleX(0) scaleY(0);
  z-index: -1;
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 4px 4px rgb(0 0 0 / 6%);
  color: #a2a2a2;
  height: fit-content;
  width: 100%;
  min-width: 128px;
  list-style: none;
}

.language_dropdown {
  position: absolute;
  top: 100%;
  left: 16px;
  right: 0;
  bottom: 0;
  padding: 4px 8px;
  font-size: 14px;
  background-color: #fff;
  transform: scaleX(0) scaleY(0);
  z-index: 1;
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 4px 4px rgb(0 0 0 / 6%);
  color: #a2a2a2;
  height: fit-content;
  width: calc(100% - 32px);
  list-style: none;
}

.header_list_dropdown .dropdown-item.active {
  color: #ed1b24;
  font-weight: 600;
}

.header_list_dropdown p {
  font-size: 14px;
}

.header_layout .navigation-primary_item-dropdown_list {
  min-width: 128px;
  display: flex;
  align-items: center;
}

.dropdown_holder {
  color: #616161;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-weight: 600;
  box-sizing: inherit;
  padding: 0 16px;
  transition: all 0.3s linear;
  line-height: 21px;
}

// Change from Adobe
.language_dropdown_item .dropdown_holder {
  padding-right: 12px;
  font-size: 14px;
  padding-left: 8px;
}

// Change from Adobe
.navigation-primary_right .language_dropdown_item:hover .dropdown_holder,
.navigation-primary_right .language_dropdown_item:hover .dropdown-arrow {
  opacity: 0.7;
}

.dropdown_arrow_icon {
  transition: all 0.3s;
}

.arrow_up {
  transform: rotate(180deg);
}

.header-navigation .discover_dropdown {
  position: absolute;
  padding: 24px;
  background: linear-gradient(181.48deg, #fff 47.95%, #ebebeb 121.09%);
  box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
    0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
  border-radius: 16px;
  left: -10px;
  top: 100%;
  z-index: 1;
  min-width: 320px;
  display: block;
  flex-direction: column;
  transform: scaleX(0) scaleY(0);
  grid-gap: 8px;
  gap: 8px;
  justify-content: space-between;
  transition: all 0.3s ease-in-out;
}

.discover_tooltip {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #616161;
  margin-bottom: 10px;
}

.discover_btn {
  font-family: "SF Pro Display";
  color: #000;
  margin-right: 5px;
  align-items: center;
}

.discover_btn:hover {
  text-decoration: underline;
}

.navigation-primary_item .link_component-link .link_text {
  color: #616161;
}

// Change from Adobe
.mobile-menu-items .navigation-primary_right .navigation-primary_item .link_component-link .link_text {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

// Change from Adobe
.navigation-primary_left .navigation-primary_item .link_component-link .link_text {
  font-weight: 600;
}

// Change from Adobe
.header_layout.prelogin-header .navigation-primary_item .link_component-link .link_text {
  font-size: 16px;
}

.navigation-primary_item .link_component-link .link_text:hover {
  color: #ed1c24;
}

.dropdown_open {
  transform: scaleX(1) scaleY(1);
}

.discover_dropdown_btn:hover .discover_dropdown {
  transform: scaleX(1) scaleY(1);
}

.language_dropdown_item {
  cursor: pointer;
  position: relative;
  text-decoration: none;
  font-weight: 400;
  font-size: 16px;
  line-height: 21px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.language_dropdown_item {
  .material-symbols-outlined {
  font-size: 16px;
  transition: all 0.3s ease;
  color: var(--secondary-mid-grey-100);
  }

  .filter-red {
    @include maxLgSemi {
      filter: brightness(0) saturate(100%) invert(29%) sepia(99%) saturate(7499%) hue-rotate(356deg) brightness(96%) contrast(117%);
    }
  }
}

.mobile-button .navigation-primary_item-dropdown_list {
  // Change from Adobe
  padding: 16px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-size: 600;
  letter-spacing: 2px;
  margin-bottom: 0;
}

// Change from Adobe
.mobile-button .navigation-primary_item-dropdown_list .dropdown_holder {
  padding: 0;
}

.mobile-button .header_list_dropdown {
  width: auto;
  // Change from Adobe
  top: 32px;
  z-index: 1;
}

// Change from Adobe
.mobile-button .navigation-primary_item-dropdown_list.open .header_list_dropdown {
  padding-left: calc(100vw / 22.5);
  padding-right: calc(100vw / 22.5);
  background-color: transparent;
  box-shadow: unset;
}

// Change from Adobe
.mobile-button .navigation-primary_item-dropdown_list.open .header_list_dropdown ul {
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid #f2f2f2;
  list-style: none;
  box-shadow: 2px 4px 4px rgba(0, 0, 0, 0.06);
  padding-inline-start: 0;
}

// Change from Adobe
.mobile-button .navigation-primary_item-dropdown_list.open .header_list_dropdown ul li {
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  letter-spacing: 2px;
}

// Change from Adobe
.mobile-button .navigation-primary_item-dropdown_list.open .header_list_dropdown ul li::marker {
  list-style: none;
}

.mobile-button .dropdown-item {
  padding: 18px 22px;
}

// Change from Adobe
.mobile-button .dropdown-item {
  list-style-position: inside;
  // Change by Adobe
  border-top: 1px solid #e3e4e6;
}

.mobile-button .dropdown-item p {
  font-family: SF Pro Display, SF Pro Display Italic, SF Pro Text, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 16px;
}

.mobile-menu .discover_dropdown_btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #a2a2a2;
}

.navigation_sub .discover_dropdown_btn .discover_dropdown {
  z-index: 1;
  display: none;
  position: relative;
  background: none;
  padding: 25px;
  height: 0;
  left: 0;
}

.navigation_sub .discover_dropdown_btn .dropdown_open {
  display: block;
  min-width: 0px;
  background: #edeef0;
  border: 1px solid black;
  border-radius: 16px;
  height: auto;
  transform: scaleX(1) scaleY(1);
}

.discover_label {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-top: 1.5rem;
  font-size: 0.875rem;
}

.discover_category {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.view_all_link {
  color: #a2a2a2;
  font-size: 12px;
  padding-left: 10px;
}

.view_all_bg {
  background-color: #333;
  padding: 8px;
}

.view_all_link:active {
  text-decoration: underline;
}

.mobile-menu .discover_dropdown .dropdown_open {
  width: 100%;
  background-color: none;
  margin: 10px 0 32px 0;
}

.mobile-menu .discover_dropdown.dropdown_open {
  width: 100%;
  background-color: unset;
  margin: 10px 0 16px 0;
}

.discover_dropdown_btn .discover_dropdown a {
  display: flex;
  align-items: center;
}

.mobile-menu .discover_category {
  grid-gap: 32px;
  gap: 32px;
  margin-top: 24px;
}

.header_layout {
  .mobile-menu .discover_category .navigation-primary_item {
    width: unset;
  }

  .navigation_sub.active {
    height: 100%;
  }

  .mobile-menu .mobile-menu-items {
    overflow-y: unset;
  }

  &.prelogin-header {
    .header-navigation {
      background-color: var(--primary-white);
      box-shadow: 0 5px 20px rgb(0 0 0 / 10%);
    }

    .navigation_primary {
      background-color: var(--primary-white);
    }

    .navigation-secondary_actions {
      height: 60px;
    }

    .navigation-primary_right .link_component-link:hover {
      color: var(--primary-white);
    }

    .language_dropdown_item {
      margin-right: 24px;
    }

    .dark & {
      color: var(--primary-black);
      a {
        color: var(--primary-black);
      }
    }
  }
}

/* END home-page */

.offer-filter__container {
  padding: 12px;
  max-width: 25%;
}

.checkbox-item__wrapper input {
  margin-right: 1rem;
}

.input__checkbox {
  max-width: 24px;
  height: 24px;
  flex: 0 0 24px;
  border-radius: 5px;
  outline: none;
  border: 1px solid #e3e4e6;
  background-color: #fff;
  position: relative;
  -webkit-appearance: none;
}

.input__radio {
  width: 1.5rem;
  height: 1.5rem;
  min-width: 1.5rem;
  margin: 0;
  -webkit-appearance: none;
  border-radius: 50%;
  outline: none;
  border: 1px solid #e3e4e6;
  position: relative;
}

.input__radio:checked {
  border-color: #ed1b24;
  background-color: transparent;
}

.input__checkbox:checked {
  border-color: #ed1b24;
  background-color: #ed1b24;
}

.checkbox-item__wrapper input:checked::before {
  opacity: 1;
}

.input__checkbox::before {
  content: "";
  position: absolute;
  width: 0.4rem;
  height: 0.9rem;
  top: 50%;
  left: 50%;
  opacity: 0;
  border: solid #fff;
  border-width: 0 0.1875rem 0.1875rem 0;
  transform: rotate(45deg) translate(-0.55rem, -0.25rem);
}

.input__radio::before {
  content: "";
  position: absolute;
  width: 0.9375rem;
  height: 0.9375rem;
  top: 50%;
  left: 50%;
  opacity: 0;
  transform: translate(-50%, -50%);
  background-color: #ed1b24;
  border-radius: 50%;
}

.dropdown__primary {
  width: 100%;
  border: 0;
  margin: 0;
  display: inline-flex;
  padding: 0;
  position: relative;
  min-width: 0;
  flex-direction: column;
  vertical-align: top;
}

.offer-filter__autocomplete {
  margin-bottom: 32px;
}

.offer-filter__input-wrapper {
  padding: 9px 16px;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e3e4e6;
}

.input-text__autocomplete {
  width: 100%;
  min-width: 30px;
  padding-top: 9.5px;
  padding-right: 4px;
  padding-bottom: 9.5px;
  padding-left: 6px;
  -webkit-appearance: none;
  align-items: center;
  border: 0;
  font-size: 1rem;
}

.input-text__autocomplete:focus {
  outline: 0;
}

.promotion-filter__wrapper {
  display: flex;
  flex-wrap: wrap;
}

.offer-banner {
  position: relative;
  display: flex;
  color: #fff;
  justify-content: flex-end;
  border-radius: 8px;
  min-height: 300px;
  width: 100%;
}

.offer-banner__text {
  padding: 16px;
}

@media (min-width: 768px) {
  .offer-banner__text {
    max-width: 58.3333%;
  }
}

.banner__link {
  display: flex;
  align-items: center;
}

.banner__description {
  margin: 16px 0 24px;
  color: #dedede;
}

.banner__icon-external {
  height: 16px;
  width: 16px;
  margin-left: 12px;
}

.offer-filter__button {
  margin-bottom: 20px;
  padding: 8px;
  max-width: 100%;
  width: 100%;
}

.offer-dialog__container {
  width: 100%;
  height: 100%;
  margin: 0;
  position: fixed;
  z-index: 1300;
  background-color: #fff;
  inset: 0;
  overflow: scroll;
}

.offer-dialog__title {
  padding: 16px;
  border-bottom: 1px solid #dedede;
}

.offer-dialog__title .title {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
}

.offer-dialog__content {
  padding: 16px;
}

.offer-dialog__button {
  background-color: #000;
  color: #fff;
  padding: 16px 24px;
  border-radius: 8px;
  /* transition: all 0.3s ease-in; */
  min-width: max-content;
  width: 100%;
  display: inline-flex;
  cursor: pointer;
  border: none;
  align-items: center;
  justify-content: space-between;
}

.button-text {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5rem;
}

.select-box__container {
  transition: opacity 251ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 167ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transform: none;
  opacity: 1;
  background-color: #fff;
  z-index: 9999;
  transform-origin: 0px 32px;
  width: fit-content;
  min-width: fit-content;
}

.select-box__wrapper {
  padding-top: 8px;
  padding-bottom: 8px;
  box-shadow: 0px 5px 5px -3px rgb(0 0 0 / 20%), 0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%);
  border-radius: 4px;
  min-width: fit-content;
}

.select-box__item {
  padding-top: 6px;
  padding-bottom: 6px;
  line-height: 1.5;
  padding-left: 16px;
  padding-right: 16px;
  font-weight: 400;
  /* cursor: pointer; */
  align-items: center;
  text-decoration: none;
  list-style-type: none;
  color: #616161;
  white-space: nowrap;
}

.select-box__item:hover {
  font-weight: 600;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.87);
  background-color: rgba(0, 0, 0, 0.04);
}

.dropdown__card-type {
  text-overflow: ellipsis;
}

.material-symbols-outlined {
  font-variation-settings: "FILL" 0, "wght" 700, "GRAD" 0, "opsz" 48;
}

.dropdown__wrapper {
  position: relative;
  margin-bottom: 24px;
}

.dropdown__display {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  border: 1px solid #e3e4e6;
}

.display__text {
  color: rgba(0, 0, 0, 0.87);
}

.text--ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.display__icon {
  color: #ed1c24;
  height: 16px;
  width: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.dropdown__list {
  background-color: #fff;
  transform-origin: top;
  border: 1px solid #e3e4e6;
  position: absolute;
  z-index: 1;
  /* top: 100%; */
  top: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
}

.--bordered {
  border-radius: 8px;
}

.--shadow {
  box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
}

.dropdown--ease-transition {
  transition: all 0.3s ease-in-out;
  transition-duration: 0.3s;
  transform: scaleY(0);
}

.dropdown--cubic-transition {
  opacity: 1;
  transform: none;
  transition: opacity 324ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 216ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.dropdown--no-animation {
  /* transition: all 0.3s ease-in-out;
  transition-duration: 0.3s;
  transform: scaleY(0); */
}

.dropdown__list>ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dropdown__item {
  transition: all 0.3s ease-in-out;
  padding: 16px;
  height: 56px;
  font-size: 16px;
  cursor: pointer;
  /* background-color: #ed1c24; */
  border: 1px solid transparent;
  border-color: transparent transparent rgba(0, 0, 0, 0.05) transparent;
}

.--selected {
  background-color: rgba(0, 0, 0, 0.08);
  font-weight: 600;
}

.dropdown__item:hover {
  background-color: rgba(0, 0, 0, 0.08);
  font-weight: 600;
}

.autocomplete__wrapper {
  position: relative;
  margin-bottom: 50px;
}

.autocomplete__display {
  padding: 9px 16px;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: text;
  border: 1px solid #e3e4e6;
}

.autocomplete__input-text {
  font-size: 1rem;
  padding: 9.5px 4px;
  border: none;
  width: 100%;
  text-overflow: ellipsis;
}

.autocomplete__input-text:focus {
  outline: 0;
}

.autocomplete__item {
  transition: all 0.3s ease-in-out;
  padding: 6px 16px 6px 16px;
  line-height: 1.5;
  font-size: 16px;
  cursor: pointer;

  /* background-color: #ed1c24; */
  &.disabled {
    color: rgb(0, 0, 0, 0.54);
    cursor: default;
  }
}

.autocomplete__item:not(.disabled):hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.autocomplete__list {
  background-color: #fff;
  transform-origin: top;
  border: 1px solid #e3e4e6;
  padding: 8px 0;
  position: absolute;
  z-index: 1;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
}

.--fit-content {
  height: fit-content;
}

.autocomplete__list>ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.checkbox-item__wrapper {
  
  -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex
;
    font-size: 1pc;
    font-weight: 400;
    letter-spacing: normal;
    line-height: 1.5;
    text-transform: none;
    width: 100%;
}

.checkbox-item__wrapper input {
  margin-right: 1rem;
  cursor: pointer;
}

.input__checkbox {
  max-width: 24px;
  height: 24px;
  flex: 0 0 24px;
  border-radius: 5px;
  outline: none;
  border: 1px solid #e3e4e6;
  background-color: #fff;
  position: relative;
  -webkit-appearance: none;
}

.input__radio {
  width: 1.5rem;
  height: 1.5rem;
  min-width: 1.5rem;
  margin: 0;
  -webkit-appearance: none;
  border-radius: 50%;
  outline: none;
  border: 1px solid #e3e4e6;
  position: relative;
}

.input__radio:checked {
  border-color: #ed1b24;
  background-color: transparent;
}

.input__checkbox:checked {
  border-color: #ed1b24;
  background-color: #ed1b24;
}

.checkbox-item__wrapper input:checked::before {
  opacity: 1;
}

.input__checkbox::before {
  content: "";
  position: absolute;
  width: 0.4rem;
  height: 0.9rem;
  top: 50%;
  left: 50%;
  opacity: 0;
  border: solid #fff;
  border-width: 0 0.1875rem 0.1875rem 0;
  transform: rotate(45deg) translate(-0.55rem, -0.25rem);
}

.input__radio::before {
  content: "";
  position: absolute;
  width: 0.9375rem;
  height: 0.9375rem;
  top: 50%;
  left: 50%;
  opacity: 0;
  transform: translate(-50%, -50%);
  background-color: #ed1b24;
  border-radius: 50%;
}

.offer-filter__button {
  width: 100%;
}

.tcb-grid-card .tcb-grid-card_list {
  margin-top: 1.5rem;
}

.tcb-grid-card_list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.tcb-grid-card_item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  gap: 24px;
  border: 1px solid #dedede;
  box-shadow: 0 5px 20px -5px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.tcb-grid-card_item:hover {
  box-shadow: 0 10px 35px -10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease-in-out;
}

.tcb-grid-card_icon-image {
  width: 100px;
  height: 100px;
  object-fit: contain;
}

.small-icon .tcb-grid-card_icon-image {
  width: 25%;
}

.tcb-grid-card_title {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
}

.tcb-grid-card_actions {
  padding-top: 20px;
  padding-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.tcb-grid-card_show-more {
  font-weight: 600;
  padding-right: 24px;
  background: url(325ffa1a962f810c273b.svg) no-repeat center right;
}

.tcb-modal.youtube-modal {
  background: transparent;
  align-items: center;
  justify-content: center;
}

.tcb-modal.youtube-modal.hidden {
  display: none;
}



.tcb-modal_backdrop {
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.youtube-modal .tcb-modal_header {
  padding: 1rem;
  align-items: center;
  gap: 24px;
}

.youtube-modal .tcb-modal_title {
  line-height: 1.5;
  font-size: 1.25rem;
}

.tcb-modal_body {
  padding: 1rem;
}

#youTubeVideo {
  width: 766px;
  aspect-ratio: 1.77;
}

.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.slick-list:focus {
  outline: 0;
}

.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick-slider .slick-list,
.slick-slider .slick-track {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.slick-track:after,
.slick-track:before {
  display: table;
  content: "";
}

.slick-track:after {
  clear: both;
}

.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

[dir="rtl"] .slick-slide {
  float: right;
}

.slick-slide img {
  display: block;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
  display: none;
}

/** Responsive CSS for desktop / large screen will be placed in here **/

@media screen and (min-width: 1024px) and (max-width: 1439px) {
  .tcb-container {
    margin: 0 64px;
  }
}

/** CSS for smaller desktop **/
@media screen and (max-width: 1354px) {
  .tcb-container {
    margin: 0 64px;
  }
}

@media screen and (max-width: 1199px) {
  .tcb-container {
    margin: 0 calc(100% / 22.5);
  }
}

@media screen and (max-width: 1200px) {
  .tcb-grid-card_list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: 1024px) and (max-width: 1200px) {
  .hero-slider_content-body {
    width: 528px;
    max-width: none;
  }
}

@media screen and (min-width: 1200px) {
  .more-info .tcb-section_header {
    min-width: 320px;
  }

  /* accodrion inspire */
  .accordion-inspire-container {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 1024px) {
  .more-info_card:hover {
    transform: scale(1.008);
  }
}

@media screen and (min-width: 767px) {

  form .radio-wrapper.one-column,
  form .checkbox-wrapper.one-column {
    display: flex;
    flex-direction: column;
  }
}

/*inspire */
@media (max-width: 1439px) {
  .tcb-hero-banner.inspire .tcb-hero-banner_body .content {
    padding-left: 100px;
  }

  .tcb-hero-banner.inspire .sub-background {
    right: 0;
  }
}

@media screen and (min-width: 1025px) {

  .header_layout.inspire .navigation-secondary_menu,
  .header_layout.inspire .navigation-secondary_actions {
    display: none;
  }

  .header_layout.inspire .navigation-secondary_item {
    color: var(--gray-900);
  }
}

/** Responsive CSS for tablet / medium screen will be placed in here **/
@media screen and (max-width: 1023px) {
  .hero-slider_content-body {
    width: 60%;
    max-width: none;
  }

  .checkbox-wrapper .checkbox-item {
    flex-grow: 0;
    max-width: 50%;
    flex-basis: 50%;
  }

  .external-component {
    width: 60%;
  }
}

@media screen and (max-width: 991px) {
  .tcb-container {
    margin: 0 calc(100vw / 22.5);
  }

  /* list-card-icon */
  .list-card-icon__list-icons-container .list-icons {
    flex-wrap: wrap;
  }

  .icon-container .icon {
    width: 96px;
    height: 96px;
    margin-bottom: 24px;
  }

  .list-icons .icon-container {
    flex: 40%;
  }

  .credit-ranking-component .credits-list {
    flex-direction: column;
  }

  .tcb-tab-content_container {
    grid-template-columns: 1fr 1fr;
  }

  .more-info_body {
    flex-direction: column;
    align-items: flex-start;
  }

  .radio-wrapper .radio-item,
  .checkbox-wrapper .checkbox-item {
    flex-grow: 0;
    max-width: 100%;
    flex-basis: 100%;
  }

  .radio-wrapper,
  .checkbox-wrapper {
    min-width: 100%;
  }

  .checkbox-info-table-component .tab .row.showed {
    display: block;
    overflow: hidden;
  }

  .select-info-table-component .tab .row.showed {
    display: block;
    overflow: hidden;
  }

  form .checkbox-wrapper {
    grid-template-columns: repeat(1, 1fr);
  }

  .tcb-grid-card_list {
    grid-template-columns: repeat(2, 1fr);
  }

  .tcb-grid-card_list {
    grid-template-columns: repeat(1, 1fr);
  }

  #youTubeVideo {
    width: 466px;
    aspect-ratio: 1.77;
  }
}

@media screen and (max-width: 1024px) {

  /*header inspire */
  .header_layout.inspire .navigation-secondary_menu-inspire div {
    display: none;
  }

  /* inspire slider */
  .inspire-slider-container {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .inspire-slider-component .head-title {
    padding-left: 32px;
  }

  .inspire-slider-component .swiper-button-next,
  .inspire-slider-component .swiper-button-prev {
    height: 30px;
    width: 30px;
  }

  .inspire-slider-component .swiper-button-next {
    right: 90px;
  }

  .inspire-slider-component .swiper-button-prev {
    left: 90px;
  }

  /* inspire hero */
  .tcb-hero-banner.inspire .tcb-hero-banner_body .content {
    padding-left: 32px;
  }

  .tcb-hero-banner.inspire .sub-background {
    right: 0;
    width: 50%;
  }

  /* accordion inspire */
  .accordion-inspire-container {
    padding-left: 32px;
    padding-right: 32px;
  }
}

@media (max-width: 976px) {

  /* inspire slider */
  .inspire-slider-component .item-content .heading,
  .inspire-slider-component .item-content .description,
  .inspire-slider-component .item-content .btn {
    grid-column: span 12 / span 12;
  }

  .inspire-slider-component .item-content .heading {
    gap: 34px;
    display: flex;
    align-items: center;
  }

  .inspire-slider-component .item-content {
    margin-top: 8px;
  }

  .inspire-slider-component .item-content .description {
    margin: 20px 0;
  }

  .inspire-slider-component .item-content .btn {
    justify-content: flex-start;
  }

  .inspire-slider-component .item-content .heading p {
    font-size: 18px;
    line-height: 24px;
  }

  .inspire-slider-component .swiper {
    padding-left: 40px;
    padding-right: 40px;
  }

  .inspire-slider-component .head-title {
    padding-left: 16px;
  }

  .inspire-slider-component .swiper-button-prev {
    left: 25px;
  }

  .inspire-slider-component .swiper-button-next {
    right: 25px;
  }

  /* inspire hero */
  .tcb-hero-banner.inspire .sub-background {
    width: 60%;
  }

  .tcb-hero-banner.inspire .tcb-hero-banner_body .content {
    margin-top: 50px;
  }

  /* accordion inspire */
  .accordion-inspire-container {
    padding-left: 16px;
    padding-right: 16px;
  }

  .accordion-inspire-component .item {
    padding-left: 0;
    padding-right: 0;
  }

  .accordion-inspire-component .item-description .cols-2 {
    flex-direction: column;
  }

  .accordion-inspire-component .cols-2 div {
    margin-bottom: 20px;
  }

  .accordion-inspire-component .cols-2 div:last-child {
    margin-bottom: 0;
  }
}

/** Responsive CSS for mobile / small screen devices will be placed in here **/

@media screen and (max-width: 767px) {

  /* hero slider */
  .hero-slider_content-body {
    width: 80%;
    max-width: none;
    padding: 20px;
    gap: 0;
    min-height: 480px;
  }

  .hero-slider_slide-header {
    margin: 10px 0;
  }

  .hero-slider_slide-header::before {
    left: -32px;
  }

  .hero-slider_indicator {
    left: 20px;
  }

  .hero-slider_slide-description>ul {
    padding-left: 20px;
  }

  /* list-card-icon */
  .icon-container .icon {
    width: 65px;
    height: 65px;
    margin-bottom: 16px;
  }

  /* list-card-icon stack */
  .list-card-icon__list-icons-container .list-icons.mobile-stack {
    flex-direction: column;
    gap: 16px;
  }

  .list-icons.mobile-stack .icon-container {
    flex-direction: row;
  }

  .mobile-stack .icon-container .icon {
    width: 65px;
    height: 65px;
  }

  .mobile-stack .icon-container .content {
    flex: 3;
  }

  .fullscreen-slider .fullscreen-slider__carousel-img {
    margin-top: 32px;
  }

  .tcb-tab-content_container {
    grid-template-columns: 1fr;
  }

  .more-info_card-scroller::-webkit-scrollbar {
    display: none;
  }

  .more-info_card-list {
    width: calc(100% + 40px);
    margin-left: -20px;
  }

  .more-info_card-scroller {
    padding: 0 20px;
    overflow-x: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .more-info_card {
    min-width: calc(100vw - 80px);
  }

  .more-info_body .tcb-scroll-controls {
    display: block;
  }

  .form-wrapper.checkbox-block-grid .information-blockform-halfitem {
    flex-grow: 0;
    max-width: 100%;
    flex-basis: 100%;
  }

  .form-wrapper.checkbox-block-grid .list-dropdown {
    min-width: 90%;
  }

  .tcb-icon.stock-down {
    width: 100%;
    margin-bottom: 16px;
  }

  .external-component {
    width: 80%;
  }

  form .radio-wrapper,
  form .checkbox-wrapper {
    display: flex;
    flex-wrap: wrap;
  }

  .form-item:nth-child(odd) {
    padding-right: 0;
  }

  .text-columns .content-wrapper {
    display: block;
    padding: 32px 0;
  }

  .text-columns .right .content {
    border-left: none;
    padding-left: 0;
  }

  .text-columns .column {
    padding: 12px 0;
  }

  .page-menu {
    max-width: 100vw;
  }

  .select-info-table-component .select-filter {
    margin: 0 calc(100vw / 22.5);
  }

  /* loan-real-estate.html */
  .insurance-calculation.no-padding-mobile {
    padding: 0;
  }

  .insurance-calculation.no-padding-mobile .panel-inputs {
    padding: 8px;
  }

  .insurance__fordevelopment .loan-realestate__container .insurance-calculation {
    padding-top: 0;
  }

  .insurance__fordevelopment.insurance-calculation .panel-info__content-button a {
    width: fit-content;
  }

  .insurance__fordevelopment .insurance-calculation .item__label .loan-realestate__des {
    max-width: none;
  }

  .tcb-button.tcb-button--hover-gray {
    max-width: 100%;
  }

  .insurance-calculation .item__label .loan-realestate__des {
    max-width: 100%;
  }

  .article-content {
    max-width: 100%;
  }

  /* card-list.html */
  .card-list .card-list__fordevelopment .card-list__item-body {
    height: 230px;
  }

  .card-list .card-list__fordevelopment .slick-slide {
    width: 40% !important;
  }

  .card-list .card-list__list-item.card-list__fordevelopment .card-list__item {
    max-width: 100%;
    flex-basis: 100%;
  }

  .card-list .card-list__fordevelopment .slick-list {
    overflow: auto !important;
  }

  .card-list .card-list__fordevelopment .slick-dots {
    display: none !important;
  }

  .card-list .width-50-50 {
    grid-template-columns: repeat(2, calc(90% + 16px));
  }

  .card-list .width-50-50 .card-list__item {
    max-width: 100%;
    flex-basis: 100%;
  }

  .card-list .card-list__item-container.less-shadow-hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  }

  .card-list .card-list__item-container.less-shadow-hover:hover {
    box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%),
      0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%),
      0 0.913195px 5.00873px rgb(0 0 0 / 1%);
  }

  .full-width-mobile {
    width: 100%;
  }

  .section-wrapper .list-tile__card-description.list-tile__bullet-black>ul>li::before {
    border-radius: 50%;
    background-color: #616161;
    left: 10px;
  }

  .tcb-grid-card_item {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding: 1.5rem 20px;
    border-radius: 16px;
  }

  .tcb-grid-card_icon-image {
    width: 72px;
    height: 72px;
  }

  #youTubeVideo {
    width: 100%;
    aspect-ratio: 1.77;
  }

  [card-slider-type="card-item"] .cardslider-carousel-slickitem article {
    height: 605px;
  }

  /* inspire slider */
  .inspire-slider-component .swiper {
    padding-left: 20px;
    padding-right: 20px;
    margin-left: 36px;
    margin-right: 36px;
  }

  .inspire-slider-component .swiper-button-next {
    right: 5px;
  }

  .inspire-slider-component .swiper-button-prev {
    left: 5px;
  }

  .inspire-slider-component .head-title {
    font-size: 24px;
    padding-left: 6px;
  }

  .inspire-slider-component .item-img .btn-video svg {
    width: 47px;
    height: 47px;
  }

  .tab-item_list.tcb-tab-content[data-tab-style="image-slider"] {
    height: 700px;
  }


}

@media (max-width: 718px) {

  /* accordion inspire */
  .accordion-inspire-component .faq-panel-container .right-container {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

@media (max-width: 650px) {

  /* inspire slider */
  .inspire-slider-container {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .inspire-slider-component .swiper {
    padding-left: 16px;
    padding-right: 16px;
  }

  .inspire-slider-component .item-content br {
    display: none;
  }

  .inspire-slider-component .item-content .heading {
    display: unset;
  }
}

@media (max-width: 640px) {

  /* inspire hero */
  .tcb-hero-banner.inspire .tcb-hero-banner_content.tcb-hero-banner_content--medium {
    min-height: 650px;
  }

  .tcb-hero-banner.inspire .sub-background {
    display: none;
  }

  .tcb-hero-banner.inspire .btn {
    display: none;
  }

  .tcb-hero-banner.inspire .content .text-img img {
    max-width: 210px;
  }

  .tcb-hero-banner.inspire .tcb-hero-banner_body .content {
    padding-top: 285px;
  }

  [card-slider-type="card-item"] .cardslider-carousel-slickitem article {
    height: 510px;
  }
}

@media (max-width: 575px) {
  .select-filter h2 {
    margin-bottom: 12px;
    width: 100%;
  }

  .select-checkbox-filter {
    display: block;
  }

  .select-checkbox-filter h2 {
    padding-bottom: 24px;
  }

  .select-checkbox-filter .select-options {
    margin-left: 0;
  }

  .checkbox-form-container .information-blockform-content .title-cmp {
    padding-bottom: 12px;
  }

  .card-list .card-list__fordevelopment .card-list__item-body {
    height: 250px;
  }

  .statistics-table-component .select-checkbox-filter {
    padding-bottom: 0;
  }

  /* inspire slider */
  .inspire-slider-component .swiper {
    margin-left: 0;
    margin-right: 0;
  }

  .inspire-slider-component .head-title {
    padding-left: 16px;
  }
}

@media screen and (max-width: 480px) {
  .hero-slider_content-body {
    width: 100%;
    max-width: none;
    padding: 20px;
    gap: 0;
  }

  .hero-slider_indicator {
    left: 5px;
  }


  [card-slider-type="card-item"] .cardslider-carousel-slickitem article {
    height: 510px;
  }
}

@media (max-width: 468px) {

  /* inspire hero */
  .tcb-hero-banner.inspire .tcb-hero-banner_content.tcb-hero-banner_content--medium {
    min-height: 554px;
  }
}

/* Component Floating Banner */
@media (max-width: 767px) {
  .floating-banner .floating-banner__content {
    flex-direction: column;
    align-items: flex-start;
    grid-gap: 8px;
    gap: 8px;
  }

  .floating-banner .floating-banner__bg-img-desktop {
    display: none;
  }

  .floating-banner .floating-banner__bg-img-mobile {
    display: block;
  }
}

/* Component Floating Banner */

/* Component Floating Toolbar */
@media (max-width: 767px) {
  .floating-toolbar .floating-toolbar__icon {
    padding: 8px 7px;
    height: 40px;
    min-width: 40px;
  }
}

/* End of Component Floating Toolbar */

/* Component Scroll to top */
@media (max-width: 767px) {
  .scroll-to-top {
    right: 40px;
  }
}

/* End of Component Scroll to top */

.hidden-tracking {
  display: none;
}

.margin-left-24px {
  margin-left: -24px;
}
