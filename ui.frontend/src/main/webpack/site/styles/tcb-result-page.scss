.tcb-result-page {
  max-width: 120rem;
  width: 100%;
  padding-top: 2rem;
  border-bottom: 0.375rem solid var(--secondary-mid-grey-40);
  display: flex;
  flex-direction: column;

  &.p-top-0 {
    padding-top: 0;
  }

  @include maxSm {
    padding-top: 1.25rem;
    border-bottom: none;
  }

  .tcb-hero-content-result {
    max-width: 90rem;
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 2rem;

    .offer-cards__wrapper,
    .offer-cards-member {
      border-bottom: 0.375rem solid var(--secondary-mid-grey-40);
      padding-bottom: 3rem;

      .offer-filter__count-container {
        align-items: center;
        display: flex;
        justify-content: space-between;
        margin-bottom: 1.5rem;

        .promotion-total-count {
          margin-bottom: 0;
        }

        @include maxSm {
          justify-content: flex-end;

          .offer-filter__dropdown {
            display: none;
          }
        }

        .dropdown__wrapper {
          margin-bottom: 0;

          .dropdown__list {
            overflow: unset;
            scrollbar-width: none;
            -ms-overflow-style: none;
            right: 0;
            min-width: 13rem;
            max-height: unset;

            &::-webkit-scrollbar {
              display: none;
            }
          }

          .dropdown__display {
            gap: 0.25rem;
            background-color: transparent;
            border: none;
            padding: 0;

            .display__title {
              font-weight: 600;
              font-size: 1rem;
              line-height: 1.5rem;
              letter-spacing: 0;
            }

            .display__text {
              color: var(--primary-black);
              font-weight: 400;
              font-size: 0.9375rem;
              line-height: 1.5rem;
              letter-spacing: 0;
            }

            img {
              width: 1rem;
              height: 1rem;
            }
          }

          .dropdown__item {
            height: auto;
            font-weight: 400 !important;
            border: none;
            padding: .625rem 1rem;
          }
        }
      }

      @include maxSm {
        &.promotion-private-mobile {
          background-color: #e9e2d5;
          padding: 3rem 0;
          .group-card {
            .card {
              background-color: transparent !important;
              padding-top: 0;
            }
          }
        }
        border-bottom: none;
        gap: 1.5rem;
      }

      .promotion-total-count {
        font-weight: 400;
        font-size: 1rem;
        line-height: 1.5rem;
        letter-spacing: 0;
        color: var(--primary-black);
        margin-bottom: 1.5rem;
      }

      .promotion-add-more {
        margin-top: 1.5rem;

        .button-more-promotion {
          display: inline-flex;
          gap: 0.5rem;
          cursor: pointer;

          .add-more-text {
            font-weight: 600;
            font-size: 1rem;
            line-height: 1.5rem;
            letter-spacing: 0;
            vertical-align: middle;
            color: var(--primary-black);
          }

          .icon-promotion-more {
            display: flex;
            justify-content: center;
            align-items: center;

            img {
              width: 1rem;
            }
          }
        }
      }

      .promotion-private {
        font-weight: 600;
        font-size: 1.5rem;
        line-height: 2rem;
        letter-spacing: 0;
        vertical-align: middle;
        color: var(--secondary-grey-100);
        @include maxSm {
          padding: 0 1rem;
        }
      }
    }
  }

  .tcb-result-page-hero-notfound {
    max-width: 90rem;
    width: 100%;
    margin: 0 auto;

    @include maxSm {
      padding: 0 1rem;
    }

    .group-notfound__wrapper {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      width: 100%;

      .notif-notfound {
        font-weight: 400;
        font-size: 1rem;
        line-height: 1.5rem;
        letter-spacing: 0;
        color: var(--primary-black);
      }

      .notfound-promotion-dropdown {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        .notfound-tilte-type-brand {
          font-weight: 400;
          font-size: 1rem;
          line-height: 1.5rem;
          letter-spacing: 0;
          color: var(--primary-black);
        }

        .notfound-input-dropdown-list {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;

          .notfound-input-dropdown-item {
            border-radius: 2.0625rem;
            padding: 0.5rem 0.75rem;
            border: 0.0625rem solid var(--primary-color-gray-500);
            cursor: pointer;
          }
        }
      }
    }
  }
}
