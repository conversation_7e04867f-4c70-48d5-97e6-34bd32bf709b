.tcb-promotion-card {
  --col-grid: 4;
  .group-card {
    display: grid;
    grid-template-columns: repeat(var(--col-grid), 1fr);
    gap: 1.5rem;
    row-gap: 2.5rem;

    &.tab-content {
      .card {
        display: none;
      }
    }

    .card {
      background-color: var(--primary-white);
      position: relative;
      border-radius: 0.5rem;
      overflow: hidden;

      .card__link-mobile {
        display: none;
      }

      .fav-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .card__subtitle_mb {
      display: none;
    }

    .card__image {
      img {
        width: 100%;
        height: auto;
        object-fit: cover;
        display: flex;
        aspect-ratio: 1.333;
      }
    }

    .group_card__content {
      padding: 1rem;
    }

    .card__content {
      margin-bottom: 0.75rem;
      overflow: hidden;

      .card__title {
        margin-bottom: 0.25rem;
        font-weight: 600;
        font-size: 1rem;
        height: 1.5rem;
        line-height: 1.5rem;
        overflow: unset;
        display: inline-block;
        max-width: max-content;
        white-space: nowrap;
      }

      .card__subtitle {
        font-size: 0.875rem;
        color: var(--accent);
        font-weight: 600;
        margin-bottom: 0.75rem;
        line-height: 1.313rem;
        text-transform: uppercase;
        letter-spacing: 0.125rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        height: 2.625rem;
      }

      .card__description {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-bottom: 0.75rem;
        height: 3rem;
      }

      .card__date {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-height: 1.5rem;

        span {
          color: var(--secondary-grey-60);
        }
      }
    }

    .card__button {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 600;
      cursor: pointer;

      &:hover {
        color: var(--primary-red);
      }

      a {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;

        &:hover {
          color: var(--primary-red);
        }
      }
    }

    .progress-bar-container {
      width: 2rem;
      height: 0.25rem;
      background-color: var(--secondary-mid-grey-60);
      border-radius: 0.25rem;
      overflow: hidden;
      display: none;

      .progress-bar {
        height: 100%;
        background-color: var(--accent);
        width: 0;
        border-radius: 0.313rem;
      }
    }

    @include maxLgSemi {
      grid-template-columns: repeat(max(1, calc(var(--col-grid) - 1)), 1fr);
    }

    @include maxMd {
      grid-template-columns: repeat(max(1, calc(var(--col-grid) - 2)), 1fr);
      gap: 0.75rem;
    }

    @include xs {
      grid-template-columns: repeat(1, 1fr);
      gap: 0.375rem;

      .card__button {
        display: none;
      }

      .card__content {
        margin-bottom: 0;
      }

      .card__date {
        font-size: 0.875rem;
        flex-flow: column-reverse;
        width: 85%;
        align-items: baseline !important;

        .progress-bar-container {
          width: 100%;
        }
      }

      .group_card__subtitle_mb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        flex-wrap: wrap;
      }

      .card__subtitle_mb {
        display: block;
        font-size: 0.813rem;
        color: var(--accent);
        font-weight: 600;
        line-height: 1.313rem;
        border: 0.063rem solid var(--secondary-light-grey-80);
        text-transform: uppercase;
        letter-spacing: 0.125rem;
        padding: 0.25rem 0.375rem;
        border-radius: 0.313rem;
      }

      .card__subtitle {
        display: none !important;
      }

      .card {
        padding: 1rem;
        position: relative;

        .card__link-mobile {
          display: block;
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
        }

        .fav-icon {
          position: absolute;
          bottom: 1rem;
          right: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .group_card__content {
        padding: 0;
      }

      .card__content {
        .card__description {
          margin-bottom: 0.25rem;
        }
      }

      .card .group_card {
        display: flex;
        gap: 0.75rem;

        .card__image {
          width: 7.75rem;

          img {
            border-radius: 0.563rem;
            width: 7.75rem;
          }
        }
      }
    }
  }

  .tcb-card__group {
    .group-card__label-mobile {
      margin-bottom: 0.75rem;
      gap: 0.5rem;
      flex-wrap: wrap;
      display: none;
    }

    .card__label-mobile {
      font-weight: 500;
      font-size: 0.75rem;
      line-height: 1.3125rem;
      letter-spacing: 0.125rem;
      text-transform: uppercase;
      color: var(--primary-black);
      padding: 0.25rem 0.375rem;
      border: 0.0625rem solid var(--secondary-light-grey-80);
      border-radius: 0.3125rem;
    }

    &.new-group-card {
      @include maxSm {
        grid-template-columns: repeat(1, 1fr);
        gap: 2.5rem;
      }
    }

    .card {
      background-color: transparent;

      &.new-card {
        .group-card__label-mobile {
          @include maxSm {
            display: flex;
          }
        }

        .tcb-card__group_card {
          @include maxSm {
            display: flex;
            gap: 0.75rem;
            flex-wrap: nowrap;
            flex-direction: row;

            .group-card__label {
              display: none;
            }

            .card__image {
              flex-basis: 33.33%;

              .tcb-thumbnail {
                border-radius: 0.5rem;
              }
            }

            .group_card__content {
              flex-basis: 66.66%;
              padding-top: 0;
            }
          }
        }
      }

      .tcb-card__link {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }

      .fav-icon {
        top: 0;
        bottom: unset;

        @include maxSm {
          top: 0;
          right: 0;

          img {
            width: 1.5625rem;
          }
        }
      }
    }
  }

  .tcb-card__content {
    &.card__content {
      margin-bottom: 0;
      display: flex;
      flex-direction: column;
      height: 100%;

      .group-card__label {
        flex-grow: 1;

        .card__label {
          display: flex;
          margin-bottom: 1rem;
          gap: 0.5rem;
          flex-wrap: wrap;
          font-weight: 600;
          font-size: 0.875rem;
          line-height: 1.3125rem;
          letter-spacing: 0.125rem;
          text-transform: uppercase;
          color: var(--secondary-gray);
        }
      }

      .card__title {
        color: var(--secondary-grey-60);
        margin-bottom: 0.25rem;
        font-weight: 400;
      }

      .card__description {
        >* {
          font-weight: 500;
        }

        margin-bottom: 0.5rem;

        @include maxSm {
          margin-bottom: 0.25rem;
        }
      }

      .card__date {
        flex-flow: unset;
        width: 100%;

        span {
          font-weight: 400;
          font-size: 1rem;
          line-height: 1.5rem;
          letter-spacing: 0;
        }

        @include maxSm {
          align-items: center !important;
        }

        .progress-bar-container {
          width: 2rem;
        }
      }
    }
  }

  .tcb-card__group_card {
    display: flex;
    flex-direction: column;
    height: 100%;

    .card__image {
      position: relative;
      border-radius: 0.5rem;
      overflow: hidden;
    }

    .card__image:has(.private-promo-card) {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    .private-promo-card {
      width: 100%;
      position: absolute;
      bottom: 0;

      img {
        height: 100% !important;
        object-fit: contain;
        aspect-ratio: unset !important;
      }
    }

    .group_card__content {
      padding: 1rem 0 0 0;
      flex-grow: 1;

      @include maxSm {
        padding: 0.75rem 0 0 0;
      }
    }
  }

  &.scroll-horizontal-card {
    .group-card {
      overflow-x: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }

      @include maxXl {
        grid-auto-flow: column;
        grid-auto-columns: 19.375rem;
        grid-template-columns: none;
        gap: 1rem;
      }

      @include maxSm {
        grid-auto-columns: 15rem;
      }

      .card {
        padding: 0 !important;

        &:nth-child(n+5){
          display: none;
        }
      }
    }
  }
}