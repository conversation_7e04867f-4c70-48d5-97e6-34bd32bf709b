import { BaseComponent } from './base';
import { TYPE_PROMOTION_SEARCH, TYPE_PROMOTION_FILTER, CLASS_HIDDEN_ELEMENT } from './constants/offer-common';
import { renderViewCard } from './utils/get-data-result-page.util';
import { getQueryParam, setURLParams, removeURLParams } from './utils/params.util';
import { getParamNames, TARGET_MAP_PARAM } from './utils/promotion-generate-endpoint.util';

export class PromotionHubTarget extends BaseComponent {
  constructor() {
    super();
    this.init();
  }

  init() {
    const $items = $('.tcb-promotion-hub-target .dropdown__item');
    if (!$items.length) {
      return;
    }

    $items.on('click', (event) => {
      const $item = $(event.target);
      const value = $item.data('value');
      const param = $item.data('param');
      const $dropdown = $('.dropdown__item');
      const typeTarget = getQueryParam(getParamNames().target);
      if (typeTarget === 'product') {
        TARGET_MAP_PARAM['product'].map((item) => {
          removeURLParams(item, true);
        });
      }

      if ($dropdown.length && typeof param === 'string' && typeof value !== 'undefined') {
        $dropdown.removeClass(CLASS_HIDDEN_ELEMENT);
        $item.addClass(CLASS_HIDDEN_ELEMENT);

        setURLParams(param, value, true);

        if (getQueryParam('q')) {
          renderViewCard(TYPE_PROMOTION_SEARCH);
        } else {
          renderViewCard(TYPE_PROMOTION_FILTER);
        }
      }
    });

    this.loadDefault($items);
  }

  loadDefault($items) {
    $items.each(function () {
      const $item = $(this);
      const param = $item.data('param');
      const value = $item.data('value');

      if (typeof param === 'string' && typeof value !== 'undefined') {
        const urlValue = getQueryParam(param);

        if (urlValue) {
          const urlValues = urlValue.split(',');
          const itemValues = String(value).split(',');

          const isMatch = itemValues.every((v) => urlValues.includes(v));

          if (isMatch) {
            $item.addClass(CLASS_HIDDEN_ELEMENT);
          }
        }
      }
    });
  }
}
