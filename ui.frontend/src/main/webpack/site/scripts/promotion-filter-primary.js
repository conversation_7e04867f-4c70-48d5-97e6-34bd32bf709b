import { BaseComponent } from './base';
import { LocationUtil } from './utils/location.util';

export class PromotionFilterPrimary extends BaseComponent {
  constructor() {
    super();
    this.CLASS_FILTER_SELECTED = 'filter-selected';
    this.init();
  }

  typesTilter = {
    cardTypes: 'card-types',
    products: 'products',
    types: 'types',
  };

  urlParams = new URLSearchParams(location.search);
  paramCardTypes = LocationUtil.getUrlParamObj()[this.typesTilter.cardTypes] || '';
  paramProducts = LocationUtil.getUrlParamObj()[this.typesTilter.products] || '';
  paramTypes = LocationUtil.getUrlParamObj()[this.typesTilter.types] || '';

  initRenderUIFilter() {
    const promotionFilterPrimary = $('.promotionfilterprimary');
    const listFilterItem = $('.credit-card-listing__button');
    if (!promotionFilterPrimary.length || !listFilterItem.length) {
      return;
    }

    listFilterItem.each((_, el) => {
      const $item = $(el);
      const dataValueRaw = ($item.data('value') || '').toLowerCase();
      const dataParams = ($item.data('param') || '').toLowerCase();
      const dataValues = dataValueRaw.split(',');

      $item.on('click', (e) => {
        e.preventDefault();
        this.handleEventClickFilter($item);
      });

      let paramList = [];
      if (dataParams === this.typesTilter.cardTypes) {
        paramList = this.convertStringParamsToArray(this.paramCardTypes);
      } else if (dataParams === this.typesTilter.products) {
        paramList = this.convertStringParamsToArray(this.paramProducts);
      } else if (dataParams === this.typesTilter.types) {
        paramList = this.convertStringParamsToArray(this.paramTypes);
      }

      const hasAllSelected = dataValues.every((val) => paramList.includes(val));

      if (hasAllSelected) {
        $item.addClass(this.CLASS_FILTER_SELECTED);
      }
    });
  }

  convertStringParamsToArray(param) {
    if (!param) {
      return [];
    }
    return param.split(',');
  }

  handleUpdateParamsUrl(param, typeParam, isRemove) {
    if (!param) {
      return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const oldValue = urlParams.get(typeParam) || '';
    let list = [];
    if (oldValue) {
      list = oldValue.split(',');
    }

    if (isRemove) {
      list = list.filter((item) => item !== param);
    } else if (!list.includes(param)) {
      list.push(param);
    }

    if (list.length) {
      urlParams.set(typeParam, list.join(','));
    } else {
      urlParams.delete(typeParam);
    }

    let newUrl = window.location.pathname;
    if (urlParams.toString()) {
      newUrl += `?${urlParams.toString()}`;
    }

    window.history.pushState({}, '', newUrl);

    this.paramCardTypes = LocationUtil.getUrlParamObj()[this.typesTilter.cardTypes] || '';
    this.paramProducts = LocationUtil.getUrlParamObj()[this.typesTilter.products] || '';
    this.paramTypes = LocationUtil.getUrlParamObj()[this.typesTilter.types] || '';
  }

  handleEventClickFilter($item) {
    const dataValueRaw = ($item.data('value') || '').toLowerCase();
    const dataParams = ($item.data('param') || '').toLowerCase();
    const dataValues = dataValueRaw.split(',');
    const isSelected = dataValues.every((val) => {
      return (
        this.convertStringParamsToArray(this.paramCardTypes).includes(val) ||
        this.convertStringParamsToArray(this.paramProducts).includes(val) ||
        this.convertStringParamsToArray(this.paramTypes).includes(val)
      );
    });

    $item.toggleClass(this.CLASS_FILTER_SELECTED, !isSelected);

    dataValues.forEach((val) => {
      this.handleUpdateParamsUrl(val, dataParams, isSelected);
    });
  }

  init() {
    this.initRenderUIFilter();
  }
}
