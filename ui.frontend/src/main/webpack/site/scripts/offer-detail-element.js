import DOMPurify from 'dompurify';
import { BaseTemplateComponent } from './base-template-component';
import { PREVIOUS_ARRAY_PAGE_KEY_NAME, screenSmMax } from './constants/common';
import { clearSearchParams, fetchData, removeHTMLTags, resetHistory, updateSearchParams,
 handleResetDropdownLanguage, disableBodyScroll } from './offer-helper';
import { SessionStorageUtil } from './utils/storage.util';
import { ACCORDION_OPEN_STYLE, SELECTOR_DETAIL_LINK } from './constants/offer-common';

const paramsName = {
  sort: 'sort',
  products: 'products',
  types: 'types',
  partner: 'partner',
  limit: 'limit',
  offset: 'offset',
  detail: 'detail',
  detailName: 'name',
  cardTypes: 'card-types',
};
const RELATED_LIMIT = 5;
const RELATED_OFFSET = 0;
const OFFSET_SCROLL_LOCATION = 48;
const SPEED_SCROLL_LOCATION = 500;
const LOCATION_SECTION_ID = '#offer-address';

export class OfferDetailElement extends BaseTemplateComponent {
  detailPlaceholder = $('#detail-container');
  offerListingComponent = $('.offer-listing-filter__body');
  timer = null;
  footer = document.getElementsByTagName('footer')[0];

  detailName = null;
  productTypes = [];
  cardTypes = [];
  categories = [];
  renderPromotionCardItem = () => { };
  renderExpiryDate = (_isRelated, _element) => { };
  showPreviewModalListener = () => { };

  selectorPdfLink = `.related-listing .card-content__link a[href]:not(${SELECTOR_DETAIL_LINK})`;
  selectorDetailLink = `.related-listing .card-content__link a${SELECTOR_DETAIL_LINK}`;

  constructor() {
    super('offer-listing-detail-template');
  }

  appendSlotContent(tagName, slot, content, isHTML = false) {
    const element = document.createElement(tagName);
    element.setAttribute('slot', slot);
    if (isHTML) {
      element.innerHTML = DOMPurify.sanitize(content);
    } else {
      element.textContent = content;
    }
    this.appendChild(element);
  }

  appendSlotHTMLContent(tagName, slot, content) {
    this.appendSlotContent(tagName, slot, content, true);
  }

  getCardTypesPrefix(tagItems) {
    return tagItems?.map((item) => {
      const prefix = item?.path?.split('card-types/')?.[1]?.split('/')?.[0];
      if (!prefix) {
        return item;
      }
      return {
        ...item,
        key: `${prefix}/${item.key}`
      };
    });
  }

  addContent(detailDialogData, detailData, _dataURL, dataLanguageLabel, utils) {
    const {
      description,
      expiryDate,
      merchant,
      locations,
      promotionContent,
      applicableContent,
      noteContent,
      locationSubtitle,
      url: pdfLink,
      productsSubtitle,
      locationSectionCTALabel,
      cardTypes,
      productTypes,
      categories,
      name,
      productsCTAPrimaryLink,
      productsCTASecondaryLink,
    } = detailData || {};

    const {
      imageMasthead,
      imageAppliedProducts,
      imageLocationSection,
      imageMobileMasthead,
      imageMobileAppliedProducts,
      imageMobileLocationSection,
      productsPrimaryCTALabel,
      productsSecondaryCTALabel,
      bgColorAppliedProducts,
      bgColorLocationSection,
      bgColorMasthead,
      labelLocationViewmore,
    } = detailDialogData || {};

    const { renderPromotionCardItem, renderExpiryDate, showPreviewModalListener } = utils || {};

    this.detailName = name;
    this.productTypes = productTypes;
    this.categories = categories;
    this.cardTypes = this.getCardTypesPrefix(cardTypes);

    this.renderPromotionCardItem = renderPromotionCardItem;
    this.renderExpiryDate = renderExpiryDate;
    this.showPreviewModalListener = showPreviewModalListener;
    let locationsDemo = [];

    if (promotionContent) {
      this.appendSlotHTMLContent('span', 'promotionContent', promotionContent);
    } else {
      const offerTypeSectionElement = $(this.shadowRoot).find('.offer-detail-offer-type__wrapper');
      offerTypeSectionElement?.remove();
    }

    this.addCardType(cardTypes, productsSubtitle);

    if (applicableContent) {
      this.appendSlotHTMLContent('p', 'applicableContent', applicableContent);
    } else {
      const applicableSectionElement = $(this.shadowRoot).find('.offer-detail-applicable__wrapper');
      applicableSectionElement?.remove();
    }

    if (noteContent) {
      this.appendSlotHTMLContent('p', 'noteContent', noteContent);
    } else {
      const noteSectionElement = $(this.shadowRoot).find('.offer-detail-note__wrapper');
      noteSectionElement?.remove();
    }

    locationsDemo = this.addLocation({ locations, labelLocationViewmore, locationSubtitle, pdfLink,
      locationSectionCTALabel, showPreviewModalListener });

    this.appendSlotContent('span', 'merchantTag', merchant?.map((item) => item.title)?.join(', '));
    this.appendSlotHTMLContent('h1', 'description', description);
    this.appendSlotContent('span', 'expiryDate', expiryDate);
    this.appendSlotContent('span', 'locations', locationsDemo.join(', '));

    this.handleClickClose();
    this.handleClickAllRelated();

    this.createBackground({
      imageMasthead,
      imageAppliedProducts,
      imageLocationSection,
      imageMobileMasthead,
      imageMobileAppliedProducts,
      imageMobileLocationSection,
      bgColorAppliedProducts,
      bgColorLocationSection,
      bgColorMasthead,
    });

    this.renderProductsButtons(
      productsCTAPrimaryLink,
      productsCTASecondaryLink,
      productsPrimaryCTALabel,
      productsSecondaryCTALabel,
    );

    this.getRelatedPromotions(_dataURL);

    if (this.footer) {
      this.shadowRoot.appendChild(this.footer.cloneNode(true));
      this.initFooter(this.shadowRoot);
    }
  }

  getCardTypesFilter() {
    const cardTypeInputs = $('.offer-filter__checkbox.card-checkbox:not(.card-checkbox-mobile) input');
    if (!cardTypeInputs?.length) {
      return [];
    }
    return cardTypeInputs.map((_, input) => $(input).val()?.toLowerCase()).get();
  }

  removeCardThumbWrapper() {
    const cardListWrapper = $(this.shadowRoot).find('.product-card-list');
    cardListWrapper?.remove();
  }

  addCardType(cardTypes, productsSubtitle) {
    const cardTypesFilterListing = this.getCardTypesFilter();
    const cardTypesFiltered = cardTypes.filter(
      (cardItem) => cardTypesFilterListing.includes(cardItem.key?.toLowerCase())
    );
    const cardTypesAndProduct = [...cardTypesFiltered, ...this.productTypes];

    if (!cardTypesAndProduct?.length && !productsSubtitle) {
      const productSectionElement = $(this.shadowRoot).find('.offer-detail-product__wrapper');
      productSectionElement?.remove();
    } else {
      // Render product section description
      if (productsSubtitle) {
        this.appendSlotHTMLContent('p', 'productsSubtitle', productsSubtitle);
      }

      // Render card type thumbnail
      const cardTypeThumbnails = this.createCardImages(cardTypesFiltered);
      if (!cardTypeThumbnails.length) {
        this.removeCardThumbWrapper();
      } else {
        cardTypeThumbnails.forEach((cartItem) => {
          this.appendChild(cartItem);
        });
      }

      // Render card type and product label
      cardTypesAndProduct?.forEach((item) => {
        if (item?.title) {
          this.appendSlotContent('li', 'cardTypeTitle', item.title);
        }
      });
    }
  }

  addLocation({ locations, labelLocationViewmore, locationSubtitle, pdfLink,
    locationSectionCTALabel, showPreviewModalListener  }) {
    let locationsDemo = [];

    if (locations?.length) {
      locations?.forEach((item, index) => {
        if (index < 2) {
          locationsDemo.push(item.title);
        } else if (index === 2) {
          locationsDemo.push('...');
        }
        this.appendSlotContent('li', 'locationContentItem', item.title);
      });

      if (locationsDemo?.length >= 3) {
        const locationMoreSpan = document.createElement('span');
        locationMoreSpan.setAttribute('slot', 'locationMore');

        const locationMoreLink = document.createElement('a');
        locationMoreLink.setAttribute('class', 'view-more masterhead-address__link');
        locationMoreLink.setAttribute('href', LOCATION_SECTION_ID);
        locationMoreLink.textContent = labelLocationViewmore;

        locationMoreSpan.appendChild(locationMoreLink);

        this.appendChild(locationMoreSpan);
        locationMoreSpan.addEventListener('click', (event) => {
          this.handleClickLocationMore(event, this.shadowRoot);
        });
      }
    } else {
      $(this.shadowRoot).find('.location-info-container').remove();
    }

    if (locationSubtitle) {
      this.appendSlotHTMLContent('p', 'locationSectionSubtitle', locationSubtitle);

      if (pdfLink && locationSectionCTALabel) {
        const locationLinkSlot = document.createElement('a');
        locationLinkSlot.setAttribute('class', 'link-custom');
        locationLinkSlot.setAttribute('slot', 'locationLink');
        locationLinkSlot.setAttribute('target', '_blank');
        locationLinkSlot.setAttribute('href', pdfLink);
        locationLinkSlot.textContent = removeHTMLTags(locationSectionCTALabel);
        this.appendChild(locationLinkSlot);
        locationLinkSlot.addEventListener('click', (event) => {
          showPreviewModalListener(event);
        });
      }
    } else if (!locations?.length) {
      const locationSectionElement = $(this.shadowRoot).find('.offer-detail-address__wrapper');
      locationSectionElement?.remove();
    }

    return locationsDemo;
  }

  initFooter(shadowRoot) {
    $(shadowRoot).find('.footer-expand').on('click', function () {
      const showHide = $(shadowRoot).find('#footer-links__list');
      const expand = this.lastElementChild;
      if (this.timer) {
        clearTimeout(this.timer);
      }
      if (!showHide.attr('class').includes('expanded')) {
        showHide.addClass('expanded');
        expand.style.transform = 'rotate(-180deg)';
      } else {
        showHide.removeClass('expanded');
        expand.style.transform = 'rotate(-360deg)';
      }
      this.timer = setTimeout(() => {
        $(document.body).animate(
          {
            scrollTop: document.body.scrollHeight,
          },
          400
        );
      }, 300);
    });
  }

  getRelatedEndpoint(dataURL) {
    const relatedParams = new URLSearchParams();
    relatedParams.append(paramsName.limit, RELATED_LIMIT);
    relatedParams.append(paramsName.offset, RELATED_OFFSET);
    relatedParams.append(paramsName.sort, 'related');
    let endpoint = `${dataURL}.offerlisting.json?${relatedParams.toString()}`;
    if (this.cardTypes?.length) {
      endpoint += `&${paramsName.cardTypes}=${this.cardTypes
        .map((item) => item.key)
        .join(',')
        .replaceAll('-', '%20')}`;
    }

    if (this.productTypes?.length) {
      endpoint += `&${paramsName.products}=${this.productTypes
        .map((item) => item.key)
        .join(',')
        .replaceAll('-', '%20')}`;
    }

    if (this.categories?.length) {
      endpoint += `&${paramsName.types}=${this.categories
        .map((item) => item.key)
        .join(',')
        .replaceAll('-', '%20')}`;
    }

    return endpoint;
  }

  renderPromotionRelatedCount(relatedTotal) {
    const relatedHeader = $(this.shadowRoot).find('.related-header-info');
    if (relatedTotal < RELATED_LIMIT) {
      $(this.shadowRoot).find('.see-all-btn').remove();
    } else {
      const quantityContainer = document.createElement('div');
      quantityContainer.setAttribute('class', 'quantity-container');

      const quantity = document.createElement('span');
      quantity.setAttribute('class', 'quantity');
      quantity.innerText = relatedTotal;

      quantityContainer.appendChild(quantity);
      relatedHeader.append(quantityContainer);
    }
  }

  handleExpandAccordion() {
    const offerDetailShadowRoot = this.shadowRoot;
    const offerAccorElement = offerDetailShadowRoot.querySelector('#offer-address tcb-offer-accordion')?.shadowRoot;
    const wrapper = offerDetailShadowRoot.querySelector(LOCATION_SECTION_ID);

    const windowWidth = $(window).width();
    let question = offerAccorElement?.querySelector('.question');
    let answer = offerAccorElement?.querySelector('.answer');
    question?.classList.add('active');
    Object.assign(wrapper?.style, {
      backgroundImage: wrapper?.getAttribute('data-bg-img'),
      padding: windowWidth > screenSmMax ? '3rem 0' : '2.5rem 1rem'
    });
    Object.assign(question?.style, ACCORDION_OPEN_STYLE);
    Object.assign(answer?.style, {
      height: answer.scrollHeight + 'px',
      marginBottom: '1.5rem',
    });
    let arrowImg = offerAccorElement.querySelector('img');
    arrowImg.style.transform = 'rotate(-180deg)';
  }

  handleClickLocationMore(event, shadowRoot) {
    event.preventDefault();
    const locationSection = $(shadowRoot).find(LOCATION_SECTION_ID)?.[0];
    if (locationSection) {
      const windowWidth = $(window).width();
      const locationOffsetTop = locationSection?.offsetTop;
      this.detailPlaceholder.animate({
        scrollTop: windowWidth > screenSmMax
          ? locationOffsetTop - OFFSET_SCROLL_LOCATION
          : locationOffsetTop,
      }, SPEED_SCROLL_LOCATION);
      this.handleExpandAccordion();
    }
  }

  renderRelatedPromotionCard(response) {
    const related = $(this.shadowRoot).find('.related-listing');

    related.empty();
    response.forEach((item) => {
      const element = this.renderPromotionCardItem(item);
      related.append(element);
    });

    this.renderExpiryDate(true, $(this.shadowRoot));
  }

  handleClickPdfRelated () {
    $(this.shadowRoot).find(this.selectorPdfLink).click(this.showPreviewModalListener);
  }

  handleClickDetailRelated () {
    const detailRelated = $(this.shadowRoot).find(this.selectorDetailLink);
    detailRelated.click((event) => {
      event.preventDefault();
      const detailName = $(event.currentTarget).data('name');

      const url = new URL(window.location);
      url.search = '';
      url.searchParams.set(paramsName.detail, detailName);

      window.location.href = url;
    });
  }

  getRelatedExcludeCurrent(promotions) {
    if (!promotions?.length) {
      return [];
    }

    // Remove promotion current 
    const excludedCurrent = promotions
      .map((item) => ({ ...item, isFavorite: true })) // Old logic have to set isFavorite true
      .filter((offer) => offer.name !== this.detailName);

    // Remove last if not exist promotion current
    if (excludedCurrent.length === RELATED_LIMIT) {
      excludedCurrent.pop();
    }

    return excludedCurrent;
  }

  getRelatedPromotions(dataURL) {
    const api = this.getRelatedEndpoint(dataURL);
    fetchData(api).then((promotionData) => {
      const { results, total } = promotionData || {};
      const relatedTotalExcludeCurrent = total - 1;

      const showRelatedItems = 4;
      const relatedPromotions = this.getRelatedExcludeCurrent(results);
      const relatedNotEnought4 = relatedPromotions.length < showRelatedItems;

      if (relatedNotEnought4) {
        $(this.shadowRoot).find('.offer-detail-related').remove();
      } else {
        this.renderRelatedPromotionCard(relatedPromotions);
        this.handleClickDetailRelated();
        this.handleClickPdfRelated();
        this.renderPromotionRelatedCount(relatedTotalExcludeCurrent);
      }
    });
  }

  setBackground(element, { desktop, mobile, bgColor }) {
    const jElement = $(this.shadowRoot).find(element);
    if (desktop || mobile) {
      let bgUrl = desktop;
      if (mobile && $(window).width() < screenSmMax) {
        bgUrl = mobile;
      }
      jElement?.attr('data-bg-img', `url(${bgUrl})`);
      jElement?.css('background-image', `url(${bgUrl})`);
    } else {
      jElement?.attr('data-bg-color', bgColor);
      jElement?.css('background-color', bgColor);
    }
  }

  createBackground({
    imageMasthead,
    imageAppliedProducts,
    imageLocationSection,
    imageMobileMasthead,
    imageMobileAppliedProducts,
    imageMobileLocationSection,
    bgColorAppliedProducts,
    bgColorLocationSection,
    bgColorMasthead,
  }) {
    this.setBackground('.offer-detail-masterhead__wrapper', {
      desktop: imageMasthead,
      mobile: imageMobileMasthead,
      bgColor: bgColorMasthead || '#f5f6f8',
    });
    this.setBackground('.offer-detail-product__wrapper', {
      desktop: imageAppliedProducts,
      mobile: imageMobileAppliedProducts,
      bgColor: bgColorAppliedProducts || '#e7e7ee',
    });
    this.setBackground('.offer-detail-address__wrapper', {
      desktop: imageLocationSection,
      mobile: imageMobileLocationSection,
      bgColor: bgColorLocationSection || '#ffffff',
    });
  }

  getKeyAsString(tagItems, kebabCase = false) {
    return tagItems?.map((item) => {
      let itemKey = item.key;
      if (!kebabCase) {
        itemKey = itemKey?.split('-')?.join(' ');
      }
      return itemKey;
    })?.join(',');
  }

  handleClickAllRelated() {
    const wrapperElement = $(this.shadowRoot);
    const seeAllButton = wrapperElement.find('.see-all-btn');
    seeAllButton.click((event) => {
      event.preventDefault();
      window.scrollTo({ top: 0, behavior: 'smooth' });
      resetHistory();

      const { cardTypes, products, types } = paramsName;

      const url = new URL(window.location);
      url.search = '';

      if (this.cardTypes?.length) {
        url.searchParams.set(cardTypes, this.getKeyAsString(this.cardTypes, true));
      }
      if (this.productTypes?.length) {
        url.searchParams.set(products, this.getKeyAsString(this.productTypes));
      }
      if (this.categories?.length) {
        url.searchParams.set(types, this.getKeyAsString(this.categories));
      }

      window.location.href = url;
    });
  }

  closeDetailModal() {
    disableBodyScroll(false);
    handleResetDropdownLanguage();

    const detailPlaceholder = document.getElementById('detail-container');
    detailPlaceholder?.classList.remove('open');

    this.detailPlaceholder.empty();
    this.offerListingComponent.fadeIn(400);

    resetHistory();

    $(this.shadowRoot)
          .find(this.selectorDetailLink)
          .off(this.handleClickDetailRelated);
    $(this.shadowRoot)
          .find(this.selectorPdfLink)
          .off(this.handleClickPdfRelated);
  }

  handleClickClose() {
    const wrapperElement = $(this.shadowRoot);
    const backButton = wrapperElement.find('.detail-back');
    backButton.click(() => {
      let previousArray = SessionStorageUtil.get(PREVIOUS_ARRAY_PAGE_KEY_NAME);

      if (previousArray?.length) {
        const previousURL = new URL(previousArray[previousArray.length - 1]);
        const paramsObject = {};
        previousURL?.searchParams?.forEach((value, key) => {
          paramsObject[key] = value;
        });
        updateSearchParams(paramsObject);
      } else {
        clearSearchParams(paramsName);
      }

      this.closeDetailModal();
    });
  }

  createCardImages(cardTypes) {
    if (!cardTypes?.length) {
      return [];
    }

    return cardTypes.reduce((listCard, item) => {
      if (!item.thumbnail) {
        return listCard;
      }

      const cardItem = document.createElement('div');
      cardItem.setAttribute('slot', 'cardTypeItem');

      const imageCardContainer = document.createElement('div');
      imageCardContainer.classList.add('image-card-container');

      const imageCardProduct = document.createElement('div');
      imageCardProduct.classList.add('image-card-product');

      const img = document.createElement('img');
      img.setAttribute('src', item.thumbnail);
      img.setAttribute('alt', item.title || 'card type thumbnail');
      img.onerror = () => {
        cardItem.remove();
        const $allThumbnails = $('.image-card-product img');
        if (!$allThumbnails?.length) {
          this.removeCardThumbWrapper();
        }
      };

      imageCardProduct.appendChild(img);
      imageCardContainer.appendChild(imageCardProduct);
      cardItem.appendChild(imageCardContainer);

      listCard.push(cardItem);
      return listCard;
    }, []);
  }

  renderProductsButtons(primaryLink, secondaryLink, productsPrimaryCTALabel, productsSecondaryCTALabel) {
    let hasButton = false;
    const hideButton = (selector, href, label) => {
      const primaryButtonElement = $(this.shadowRoot).find(selector);
      primaryButtonElement?.attr('href', href);
      primaryButtonElement?.toggleClass('hide block');
      primaryButtonElement?.children()?.first()?.text(label);
      return true;
    };

    if (primaryLink && productsPrimaryCTALabel) {
      hasButton = hideButton('.offer-listing-detail__product a.primary', primaryLink, productsPrimaryCTALabel);
    }
    if (secondaryLink && productsSecondaryCTALabel) {
      hasButton = hideButton('.offer-listing-detail__product a.secondary', secondaryLink, productsSecondaryCTALabel);
    }

    if (!hasButton) {
      $(this.shadowRoot).find('.offer-listing-detail__product .btn-container').remove();
    }
  }
}
