import { SLASH, LOCALE_VI } from '../constants/common';
export function formatExpiryDate(expiryDateStr, card) {
  const [day, month, year] = expiryDateStr.split(SLASH);
  const expiryDate = new Date(`${year}-${month}-${day}`);
  expiryDate.setHours(23, 59, 59, 999);

  const now = new Date();

  const lang = document.documentElement.lang || LOCALE_VI ;

  const progressBarElement = card.find('.progress-bar');

  const labelExpired = card.data('label-expired');
  const labelExpiredCountDown = card.data('label-expired-count-down');
  const textDay = card.data('day-text');
  const totalDays = 3;
  const timeLeft = expiryDate.getTime() - now.getTime();
  const daysLeft = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
  const progressPercentage = (1 - daysLeft / totalDays) * 100;

  if (daysLeft < 0) {
    // card.parents('.card').remove();
  } else if (daysLeft <= 3) {
    const hoursLeft = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutesLeft = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const secondsLeft = Math.floor((timeLeft % (1000 * 60)) / 1000);

    const hours = String(hoursLeft).padStart(2, '0');
    const minutes = String(minutesLeft).padStart(2, '0');
    const seconds = String(secondsLeft).padStart(2, '0');

    const formattedTime = `${hours}:${minutes}:${seconds}`;
    progressBarElement.css('width', `${progressPercentage}%`);
    card.find('.progress-bar-container').css('display', 'block');
    return `${labelExpiredCountDown} ${daysLeft} ${textDay} ${formattedTime}`;
  } else if (lang === LOCALE_VI) {

    const formattedDate = `${day.padStart(2, '0')}/${month.padStart(2, '0')}/${year}`;
    return `${labelExpired} ${formattedDate}`;
  } else {
    const expiryDate = new Date(`${year}-${month}-${day}`);
    const options = {day: '2-digit', month: 'short', year: 'numeric', timeZone: 'Asia/Ho_Chi_Minh'};
    const formattedDate = expiryDate.toLocaleDateString('en-GB', options).replace(/,/g, '');
    return `${labelExpired} ${formattedDate}`;
  }

}

export function renderDayExpired() {
  const $expiredDate = $('[data-promotion-card-expired]');
  if ($expiredDate.length) {
    $expiredDate.each(function () {
      const $card = $(this);
      if (!$card) {
        return;
      }

      const expiryDate = $card.data('promotion-card-expired');
      if (!expiryDate) {
        return;
      }

      const formatedExpiryDate = formatExpiryDate(expiryDate, $card);
      if (formatedExpiryDate) {
        $card.find('span').text(formatedExpiryDate);
      }
    });
  }
}
