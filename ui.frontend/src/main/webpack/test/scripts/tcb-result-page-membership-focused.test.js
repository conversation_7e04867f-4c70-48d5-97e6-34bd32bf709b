import { beforeEach, describe, expect, it, jest } from '@jest/globals';

// Mock BaseComponent to avoid HTMLElement issues
jest.mock('../../site/scripts/base.ts', () => ({
  BaseComponent: class MockBaseComponent {
    querySelector(selector) {
      return document.querySelector(selector);
    }
    querySelectorAll(selector) {
      return document.querySelectorAll(selector);
    }
  }
}));

// Mock LocationUtil
jest.mock('../../site/scripts/utils/location.util', () => ({
  LocationUtil: {
    getUrlParamObj: jest.fn()
  }
}));

// Mock params util
jest.mock('../../site/scripts/utils/params.util', () => ({
  getQueryParam: jest.fn()
}));

// Import after mocks
import { TcbResultPageMembership } from '../../site/scripts/tcb-result-page-membership';
import { LocationUtil } from '../../site/scripts/utils/location.util';
import { getQueryParam } from '../../site/scripts/utils/params.util';

describe('TcbResultPageMembership - Focused Coverage Tests', () => {
  let component;

  beforeEach(() => {
    // Reset DOM
    document.body.innerHTML = '';

    // Create a test DOM structure
    document.body.innerHTML = `
      <tcb-result-page-membership>
        <div class="tcb-card__group">
          <div class="card" data-membership="hoi-vien-priority,hoi-vien-inspire">Card 1</div>
          <div class="card" data-membership="hoi-vien-inspire">Card 2</div>
          <div class="card" data-membership="hoi-vien-priority">Card 3</div>
          <div class="card" data-membership="hoi-vien-private">Card 4</div>
          <div class="card">Card 5 (no membership)</div>
        </div>
        <span class="count-filter-membership">0</span>
        <tcb-promotion-card></tcb-promotion-card>
        <div class="tcb-result-page-membership__view-more"></div>
      </tcb-result-page-membership>
    `;

    // Reset mocks
    jest.clearAllMocks();
    LocationUtil.getUrlParamObj.mockReturnValue({});
    getQueryParam.mockReturnValue(null);

    // Create component instance
    component = new TcbResultPageMembership();
  });

  describe('Core Methods Coverage', () => {
    it('should test hasUrlParameters method', () => {
      // Test with no parameters
      getQueryParam.mockReturnValue(null);
      expect(component.hasUrlParameters()).toBe(false);

      // Test with parameters
      getQueryParam.mockReturnValue('hoi-vien-inspire');
      expect(component.hasUrlParameters()).toBe(true);
    });

    it('should test parseUrlParameter method', () => {
      // Test with null parameter
      getQueryParam.mockReturnValue(null);
      expect(component.parseUrlParameter('memberships')).toEqual([]);

      // Test with single value
      getQueryParam.mockReturnValue('hoi vien inspire');
      expect(component.parseUrlParameter('memberships')).toEqual(['hoi vien inspire']);

      // Test with multiple values
      getQueryParam.mockReturnValue('hoi vien inspire,hoi vien priority');
      expect(component.parseUrlParameter('memberships')).toEqual(['hoi vien inspire', 'hoi vien priority']);
    });

    it('should test parseUrlMemberships method', () => {
      // Test with no memberships
      getQueryParam.mockReturnValue(null);
      expect(component.parseUrlMemberships()).toEqual([]);

      // Test with single membership
      getQueryParam.mockReturnValue('hoi vien inspire');
      expect(component.parseUrlMemberships()).toEqual(['hoi-vien-inspire']);

      // Test with multiple memberships
      getQueryParam.mockReturnValue('hoi vien inspire,hoi vien priority');
      expect(component.parseUrlMemberships()).toEqual(['hoi-vien-inspire', 'hoi-vien-priority']);
    });

    it('should test getAllCards method', () => {
      const cards = component.getAllCards();
      expect(cards.length).toBe(5);
      expect(cards[0].textContent).toBe('Card 1');
    });

    it('should test getAllFilterableElements method', () => {
      const elements = component.getAllFilterableElements();
      expect(elements.length).toBe(4); // Only elements with data-membership
    });

    it('should test getCurrentFilters method', () => {
      // Test with no memberships
      getQueryParam.mockReturnValue(null);
      expect(component.getCurrentFilters()).toEqual({});

      // Test with memberships
      getQueryParam.mockReturnValue('hoi vien inspire');
      const filters = component.getCurrentFilters();
      expect(filters).toHaveProperty('membership');
      expect(filters.membership).toEqual(['hoi-vien-inspire']);
    });

    it('should test elementMatchesDataAttribute method', () => {
      const element = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      
      // Test matching
      expect(component.elementMatchesDataAttribute(element, 'membership', ['hoi-vien-inspire'])).toBe(true);
      expect(component.elementMatchesDataAttribute(element, 'membership', ['hoi-vien-priority'])).toBe(true);
      
      // Test non-matching
      expect(component.elementMatchesDataAttribute(element, 'membership', ['hoi-vien-private'])).toBe(false);
      
      // Test with no data attribute
      const elementNoData = document.querySelector('.card:last-child');
      expect(component.elementMatchesDataAttribute(elementNoData, 'membership', ['hoi-vien-inspire'])).toBe(false);
    });

    it('should test elementMatchesAllFilters method', () => {
      const element = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      
      // Test with empty filters
      expect(component.elementMatchesAllFilters(element, {})).toBe(true);
      
      // Test with matching filters
      expect(component.elementMatchesAllFilters(element, { membership: ['hoi-vien-inspire'] })).toBe(true);
      
      // Test with non-matching filters
      expect(component.elementMatchesAllFilters(element, { membership: ['hoi-vien-private'] })).toBe(false);
    });

    it('should test getFilteredCards method', () => {
      // Test with no filters
      getQueryParam.mockReturnValue(null);
      const allCards = component.getFilteredCards();
      expect(allCards.length).toBe(4); // All cards with data-membership

      // Test with filters
      getQueryParam.mockReturnValue('hoi vien inspire');
      const filteredCards = component.getFilteredCards();
      expect(filteredCards.length).toBe(2); // Cards 1 and 2 match
    });

    it('should test getCardContainer method', () => {
      const container = component.getCardContainer();
      expect(container).toBeTruthy();
      expect(container.tagName.toLowerCase()).toBe('tcb-promotion-card');
    });

    it('should test getCountFilterElement method', () => {
      const countElement = component.getCountFilterElement();
      expect(countElement).toBeTruthy();
      expect(countElement.classList.contains('count-filter-membership')).toBe(true);
    });

    it('should test countFilteredCards method', () => {
      // Test with no URL parameters
      getQueryParam.mockReturnValue(null);
      expect(component.countFilteredCards()).toBe(0);

      // Test with filters
      getQueryParam.mockReturnValue('hoi vien inspire');
      expect(component.countFilteredCards()).toBe(2);
    });

    it('should test updateCountDisplay method', () => {
      const countElement = component.getCountFilterElement();
      
      // Test with no filters
      getQueryParam.mockReturnValue(null);
      component.updateCountDisplay();
      expect(countElement.getAttribute('data-count-filter-membership')).toBe('0');

      // Test with filters
      getQueryParam.mockReturnValue('hoi vien inspire');
      component.updateCountDisplay();
      expect(countElement.getAttribute('data-count-filter-membership')).toBe('2');
    });

    it('should test getMembershipElements method', () => {
      const elements = component.getMembershipElements();
      expect(elements.length).toBe(4);
      expect(elements[0].getAttribute('data-membership')).toBe('hoi-vien-priority,hoi-vien-inspire');
    });

    it('should test elementMatchesMemberships method', () => {
      const element = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      
      expect(component.elementMatchesMemberships(element, ['hoi-vien-inspire'])).toBe(true);
      expect(component.elementMatchesMemberships(element, ['hoi-vien-private'])).toBe(false);
      expect(component.elementMatchesMemberships(null, ['hoi-vien-inspire'])).toBe(false);
      expect(component.elementMatchesMemberships(element, null)).toBe(false);
    });

    it('should test filterElementsByMembership method', () => {
      const elements = document.querySelectorAll('[data-membership]');
      
      // Test filtering
      component.filterElementsByMembership(['hoi-vien-inspire']);
      
      // Check that matching elements are shown
      expect(elements[0].style.display).toBe(''); // Card 1 matches
      expect(elements[1].style.display).toBe(''); // Card 2 matches
      expect(elements[2].style.display).toBe('none'); // Card 3 doesn't match
      expect(elements[3].style.display).toBe('none'); // Card 4 doesn't match

      // Test with empty array
      component.filterElementsByMembership([]);
      elements.forEach(element => {
        expect(element.style.display).toBe('none');
      });

      // Test error handling
      expect(() => component.filterElementsByMembership(null)).toThrow();
      expect(() => component.filterElementsByMembership(undefined)).toThrow();
    });

    it('should test applyMembershipFilter method', () => {
      const showAllSpy = jest.spyOn(component, 'showAllElements');
      const filterSpy = jest.spyOn(component, 'filterElementsByMembership');

      // Test with no memberships
      getQueryParam.mockReturnValue(null);
      component.applyMembershipFilter();
      expect(showAllSpy).toHaveBeenCalled();
      expect(filterSpy).not.toHaveBeenCalled();

      // Reset spies
      showAllSpy.mockClear();
      filterSpy.mockClear();

      // Test with memberships
      getQueryParam.mockReturnValue('hoi vien inspire');
      component.applyMembershipFilter();
      expect(showAllSpy).not.toHaveBeenCalled();
      expect(filterSpy).toHaveBeenCalledWith(['hoi-vien-inspire']);
    });

    it('should test showAllElements method', () => {
      const elements = document.querySelectorAll('[data-membership]');
      
      // Hide elements first
      elements.forEach(element => {
        element.classList.add('hidden-forced');
      });

      // Show all elements
      component.showAllElements();

      // Check that all elements are shown
      elements.forEach(element => {
        expect(element.classList.contains('hidden-forced')).toBe(false);
      });
    });
  });
});
