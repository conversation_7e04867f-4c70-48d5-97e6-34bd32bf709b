import { OfferDetailElement } from "../../site/scripts/offer-detail-element";
import { describe, expect } from "@jest/globals";
import { fetchData } from "../../site/scripts/offer-helper";
import DOMPurify from "dompurify";
const SAMPLE = `
    <div id="detail-container">
        <tcb-offer-listing-detail>
        </tcb-offer-listing-detail>
    </div>
    <template id="offer-listing-detail-template">
    </template>
`;

global.$ = jest.fn(() => ({
  attr: jest.fn(),
  width: jest.fn().mockReturnValue(1440),
  animate: jest.fn(),
  empty: jest.fn(),
  fadeIn: jest.fn(),
  appendChild: jest.fn(),
  find: jest.fn(() => ({
    off: jest.fn(),
    remove: jest.fn(),
    on: jest.fn(),
    empty: jest.fn(),
    append: jest.fn(),
    addClass: jest.fn(),
    click: jest.fn(),
    removeClass: jest.fn(),
    first: jest.fn(() => ({
      text: jest.fn(),
    })),
    children: jest.fn(() => ({
      first: jest.fn(() => ({
        text: jest.fn(),
      })),
    })),
    toggleClass: jest.fn(),
    css: jest.fn(),
    attr: (type) => {
      if (typeof type === "string" && type.includes("data-language-label")) {
        return "vi";
      }
      if (typeof type === "string" && type.includes("data-search-count")) {
        return {
          replace: jest.fn(),
        };
      }
      return jest.fn().mockReturnValue("text");
    },
  })),
  toggleClass: jest.fn(),
}));

jest.mock("dompurify", () => ({
  sanitize: jest.fn((content) => content),
}));

jest.mock("../../site/scripts/offer-helper", () => ({
  clearSearchParams: jest.fn(),
  fetchData: jest.fn(),
  removeHTMLTags: jest.fn((content) => content),
  resetHistory: jest.fn(),
  updateSearchParams: jest.fn(),
  disableBodyScroll: jest.fn(),
  handleResetDropdownLanguage: jest.fn()
}));

let detailDialogData = {
  imageMasthead: "imageMasthead.jpg",
  productsPrimaryCTALabel: "Buy now",
  labelLocationViewmore: "View more locations",
};

let detailData = {
  description: "Special offer",
  cardTypes: [
    {
      key: "platinum",
      path: "techcombank:promotions/card-types/card-type-2/platinum",
      title: "Thẻ Platinum",
      thumbnail:
        "/content/dam/techcombank/public-site/promo/img/Debit-Card-Priority.png",
    },
  ],
  expiryDate: "2024-12-31",
  merchant: [{ title: "Merchant A" }, { title: "Merchant B" }],
  locations: [
    { title: "Location 1" },
    { title: "Location 2" },
    { title: "Location 1" },
  ],
  locationSectionCTALabel: "locationSectionCTALabel",
  promotionContent: "<p>Promotion details</p>",
  locationSubtitle: "<p>locationSubtitle</p>",
  productsSubtitle: "<p>productsSubtitle</p>",
  applicableContent: "Applicable in all stores",
  productTypes: [{ key: "product1", title: "Product 1" }],
  categories: [{ key: "category1", title: "Category 1" }],
  name: "Offer Name",
  productsCTAPrimaryLink: "https://example.com/buy-now",
  url: "https://example.com/buy-now/content/dam/techcombank/public-site/promo/pdf",
};

let utils = {
  renderPromotionCardItem: jest.fn(),
  renderExpiryDate: jest.fn(),
  getPromotions: jest.fn(),
  openDetailModal: jest.fn(),
  showPreviewModalListener: jest.fn(),
};

let dataURL = "https://example.com/offerlisting";
let dataLanguageLabel = "en";

describe("OfferDetailElement", () => {
  let offerDetailElement;
  customElements.define("tcb-offer-listing-detail", OfferDetailElement);
  beforeEach(() => {
    document.body.innerHTML = SAMPLE;
    offerDetailElement = new OfferDetailElement();
  });

  describe("appendSlotContent", () => {
    it("should document is ready", () => {
      expect(document.readyState).toBe("complete");
    });

    it("should append text content", () => {
      offerDetailElement.appendSlotContent(
        "a",
        "locationSectionCTALabel",
        detailData.locationSectionCTALabel
      );
      const addedElement = offerDetailElement.querySelector(
        '[slot="locationSectionCTALabel"]'
      );
      expect(addedElement.textContent).toBe(detailData.locationSectionCTALabel);
    });

    it("should append HTML content when isHTML is true", () => {
      offerDetailElement.appendSlotContent(
        "p",
        "promotionContent",
        detailData.promotionContent,
        true
      );
      const addedElement = offerDetailElement.querySelector(
        '[slot="promotionContent"]'
      );
      expect(addedElement.innerHTML).toBe(detailData.promotionContent);
    });

    it("should append tag HTML", () => {
      offerDetailElement.appendSlotHTMLContent(
        "p",
        "productsSubtitle",
        detailData.productsSubtitle,
        true
      );
      const addedElement = offerDetailElement.querySelector(
        '[slot="productsSubtitle"]'
      );
      expect(addedElement.innerHTML).toBe(detailData.productsSubtitle);
    });
  });

  describe("getRelatedEndpoint", () => {
    it("should generate the correct related endpoint URL", () => {
      offerDetailElement.cardTypes = [{ key: "credit-cards/key1" }];
      offerDetailElement.productTypes = [{ key: "product1" }];
      offerDetailElement.categories = [{ key: "category1" }];
      const result = offerDetailElement.getRelatedEndpoint(dataURL);
      expect(result).toBe(
        "https://example.com/offerlisting.offerlisting.json?limit=5&offset=0&sort=related&card-types=credit%20cards/key1&products=product1&types=category1"
      );
    });
  });

  describe("getCardTypesPrefix", () => {
    it("should received prefix", () => {
      const result = offerDetailElement.getCardTypesPrefix(
        detailData.cardTypes
      );
      expect(result).toEqual([
        {
          key: "card-type-2/platinum",
          path: "techcombank:promotions/card-types/card-type-2/platinum",
          title: "Thẻ Platinum",
          thumbnail:
            "/content/dam/techcombank/public-site/promo/img/Debit-Card-Priority.png",
        },
      ]);
    });

    it("should received encoded url", () => {
      const result = offerDetailElement.getKeyAsString(
        offerDetailElement.getCardTypesPrefix(detailData.cardTypes)
      );
      expect(result).toEqual("card type 2/platinum");
    });
  });

  describe("getRelatedPromotions", () => {
    it("should fetch related promotions and render them", async () => {
      const mockResponse = {
        results: [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }],
        total: 5,
      };
      fetchData.mockResolvedValue(mockResponse);
      offerDetailElement.renderRelatedPromotionCard = jest.fn();
      offerDetailElement.renderPromotionRelatedCount = jest.fn();

      await offerDetailElement.getRelatedPromotions("https://example.com");

      expect(offerDetailElement.renderRelatedPromotionCard).toBeCalled();
      expect(offerDetailElement.renderPromotionRelatedCount).toBeCalled();
    });

    it("renderPromotionRelatedCount", () => {
      offerDetailElement.renderPromotionRelatedCount(5);
      expect(document.readyState).toBe("complete");
    });
  });

  describe("addContent", () => {
    it("should set the correct properties and render content", () => {
      offerDetailElement.footer = {
        cloneNode: jest.fn().mockReturnValue(document.createElement("footer")),
      };

      offerDetailElement.getRelatedPromotions = jest.fn();

      offerDetailElement.addContent(
        detailDialogData,
        detailData,
        dataURL,
        dataLanguageLabel,
        utils
      );

      expect(offerDetailElement.productTypes).toEqual(detailData.productTypes);
      expect(offerDetailElement.categories).toEqual(detailData.categories);

      expect(DOMPurify.sanitize).toHaveBeenCalledWith(
        detailData.promotionContent
      );
      expect(DOMPurify.sanitize).toHaveBeenCalledWith(
        detailData.promotionContent
      );
    });
  });

  describe("closeDetailModal", () => {
    it("closeDetailModal", () => {
      offerDetailElement.closeDetailModal();
      expect(document.getElementById("detail-container").className).toBe("");
    });
  });
});
