import { beforeEach, describe, expect, it, jest } from '@jest/globals';

// Mock BaseComponent to avoid HTMLElement issues
jest.mock('../../site/scripts/base.ts', () => ({
  BaseComponent: class MockBaseComponent {}
}));

// Mock LocationUtil
jest.mock('../../site/scripts/utils/location.util', () => ({
  LocationUtil: {
    getUrlParamObj: jest.fn()
  }
}));



// Import after mocks
import { TcbResultPageMembership } from '../../site/scripts/tcb-result-page-membership';
import { LocationUtil } from '../../site/scripts/utils/location.util';

describe('TcbResultPageMembership', () => {
  let component;
  let mockElements;

  beforeEach(() => {
    // Reset DOM
    document.body.innerHTML = '';

    // Create a custom element container that mimics the actual DOM structure
    document.body.innerHTML = `
      <tcb-result-page-membership>
        <div class="tcb-card__group">
          <div class="card" data-membership="hoi-vien-priority,hoi-vien-inspire">Card 1</div>
          <div class="card" data-membership="hoi-vien-inspire">Card 2</div>
          <div class="card" data-membership="hoi-vien-priority">Card 3</div>
          <div class="card" data-membership="hoi-vien-private">Card 4</div>
          <div class="card">Card 5 (no membership)</div>
        </div>
        <span class="count-filter-membership">0</span>
        <tcb-promotion-card></tcb-promotion-card>
        <div class="tcb-result-page-membership__view-more"></div>
      </tcb-result-page-membership>
    `;

    const componentElement = document.querySelector('tcb-result-page-membership');
    mockElements = componentElement.querySelectorAll('[data-membership]');

    // Reset mocks
    jest.clearAllMocks();
    LocationUtil.getUrlParamObj.mockReturnValue({});

    // Create component instance and mock its methods to use the DOM element
    component = Object.create(TcbResultPageMembership.prototype);

    // Mock the querySelector and querySelectorAll methods to use the DOM element
    component.querySelector = (selector) => componentElement.querySelector(selector);
    component.querySelectorAll = (selector) => componentElement.querySelectorAll(selector);
    component.getAttribute = (attr) => componentElement.getAttribute(attr);
    component.classList = componentElement.classList;

    // Mock other necessary methods and properties
    component.isExpanded = false;
    component.maxInitialCards = 3;

    // Mock all the methods that are called by the component
    component.hasUrlParameters = jest.fn().mockReturnValue(false);
    component.parseUrlMemberships = jest.fn().mockReturnValue([]);
    component.parseUrlParameter = jest.fn().mockReturnValue([]);
    component.getCurrentFilters = jest.fn().mockReturnValue({});
    component.getAllFilterableElements = jest.fn().mockReturnValue(
      Array.from(componentElement.querySelectorAll('[data-membership]'))
    );
    component.getAllCards = jest.fn().mockReturnValue(
      Array.from(componentElement.querySelectorAll('.card'))
    );
    component.getFilteredCards = jest.fn().mockReturnValue(
      Array.from(componentElement.querySelectorAll('[data-membership]'))
    );
    component.elementMatchesAllFilters = jest.fn().mockReturnValue(true);
    component.elementMatchesDataAttribute = jest.fn().mockReturnValue(true);
    component.applyFiltersToElements = jest.fn();
    component.reorderCardsInDOM = jest.fn();
    component.refreshCardVisibility = jest.fn();
    component.showCardContainer = jest.fn();
    component.hideCardContainer = jest.fn();
    component.initializeCardVisibility = jest.fn();
    component.setupViewMoreButton = jest.fn();
    component.getViewMoreButton = jest.fn().mockReturnValue(
      componentElement.querySelector('.tcb-result-page-membership__view-more')
    );
    component.getCardContainer = jest.fn().mockReturnValue(
      componentElement.querySelector('tcb-promotion-card')
    );
    component.getCountFilterElement = jest.fn().mockReturnValue(
      componentElement.querySelector('.count-filter-membership')
    );
    component.countFilteredCards = jest.fn().mockReturnValue(0);
    component.updateCountDisplay = jest.fn();
    component.applyAllFilters = jest.fn();

    // Initialize the component manually without calling constructor
    component.init = jest.fn();
  });

  describe('constructor and init', () => {
    it('should create an instance and call init', () => {
      expect(component).toBeInstanceOf(TcbResultPageMembership);
    });

    it('should call applyMembershipFilter during initialization', () => {
      const spy = jest.spyOn(TcbResultPageMembership.prototype, 'applyMembershipFilter');
      new TcbResultPageMembership();
      expect(spy).toHaveBeenCalled();
      spy.mockRestore();
    });
  });

  describe('parseUrlMemberships', () => {
    it('should return empty array when no memberships parameter', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({});
      const result = component.parseUrlMemberships();
      expect(result).toEqual([]);
    });

    it('should parse single membership from URL', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual(['hoi-vien-inspire']);
    });

    it('should parse multiple memberships from URL with comma encoding', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire%2Choi+vien+priority'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual(['hoi-vien-inspire', 'hoi-vien-priority']);
    });

    it('should handle URL decoding correctly', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi%20vien%20inspire%2Choi%20vien%20priority'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual(['hoi-vien-inspire', 'hoi-vien-priority']);
    });

    it('should filter out empty memberships', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire%2C%2Choi+vien+priority'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual(['hoi-vien-inspire', 'hoi-vien-priority']);
    });

    it('should trim whitespace from memberships', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: '+hoi+vien+inspire+%2C+hoi+vien+priority+'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual(['hoi-vien-inspire', 'hoi-vien-priority']);
    });

    it('should handle null memberships parameter', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: null
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual([]);
    });

    it('should handle undefined memberships parameter', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: undefined
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual([]);
    });

    it('should handle empty string memberships parameter', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: ''
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual([]);
    });

    it('should handle whitespace-only memberships parameter', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: '   '
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual([]);
    });

    it('should handle single comma memberships parameter', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: '%2C'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual([]);
    });

    it('should handle multiple consecutive commas', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire%2C%2C%2Choi+vien+priority'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual(['hoi-vien-inspire', 'hoi-vien-priority']);
    });

    it('should handle special characters in membership names', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+special%40%23%2Choi+vien+test%26'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual(['hoi-vien-special@#', 'hoi-vien-test&']);
    });

    it('should handle very long membership names', () => {
      const longMembership = 'a'.repeat(100);
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: longMembership.replace(/\s+/g, '+')
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual([longMembership]);
    });

    it('should handle mixed encoding formats', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire%2Choi%20vien%20priority%2Choi-vien-private'
      });
      const result = component.parseUrlMemberships();
      expect(result).toEqual(['hoi-vien-inspire', 'hoi-vien-priority', 'hoi-vien-private']);
    });

    it('should handle LocationUtil throwing an error', () => {
      LocationUtil.getUrlParamObj.mockImplementation(() => {
        throw new Error('URL parsing error');
      });
      expect(() => component.parseUrlMemberships()).toThrow('URL parsing error');
    });

    it('should handle LocationUtil returning null', () => {
      LocationUtil.getUrlParamObj.mockReturnValue(null);
      expect(() => component.parseUrlMemberships()).toThrow();
    });
  });

  describe('getMembershipElements', () => {
    it('should return all elements with data-membership attributes', () => {
      const elements = component.getMembershipElements();
      expect(elements.length).toBe(4); // 4 elements with data-membership
    });

    it('should return NodeList of elements', () => {
      const elements = component.getMembershipElements();
      expect(elements).toBeInstanceOf(NodeList);
    });

    it('should return empty NodeList when no elements with data-membership exist', () => {
      document.body.innerHTML = '<div>No membership elements</div>';
      const elements = component.getMembershipElements();
      expect(elements.length).toBe(0);
      expect(elements).toBeInstanceOf(NodeList);
    });

    it('should return elements from entire document', () => {
      // Add elements to different parts of the document
      document.head.innerHTML = '<meta data-membership="test-head">';
      const elements = component.getMembershipElements();
      expect(elements.length).toBe(5); // 4 original + 1 in head
    });

    it('should handle elements with empty data-membership attributes', () => {
      // Reset DOM to ensure clean state
      document.head.innerHTML = '';
      document.body.innerHTML = `
        <div class="card" data-membership="hoi-vien-priority,hoi-vien-inspire">Card 1</div>
        <div class="card" data-membership="hoi-vien-inspire">Card 2</div>
        <div class="card" data-membership="hoi-vien-priority">Card 3</div>
        <div class="card" data-membership="hoi-vien-private">Card 4</div>
        <div class="card">Card 5 (no membership)</div>
        <div data-membership="">Empty membership</div>
      `;
      const elements = component.getMembershipElements();
      expect(elements.length).toBe(5); // Should still include element with empty attribute
    });
  });

  describe('elementMatchesMemberships', () => {
    let element1, element2, element3;

    beforeEach(() => {
      element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');
      element3 = document.querySelector('[data-membership="hoi-vien-priority"]');
    });

    it('should return true when element membership matches target', () => {
      const result = component.elementMatchesMemberships(element2, ['hoi-vien-inspire']);
      expect(result).toBe(true);
    });

    it('should return true when element has multiple memberships and one matches', () => {
      const result = component.elementMatchesMemberships(element1, ['hoi-vien-inspire']);
      expect(result).toBe(true);
    });

    it('should return true when multiple targets and one matches', () => {
      const result = component.elementMatchesMemberships(element3, ['hoi-vien-inspire', 'hoi-vien-priority']);
      expect(result).toBe(true);
    });

    it('should return false when no membership matches', () => {
      const result = component.elementMatchesMemberships(element2, ['hoi-vien-private']);
      expect(result).toBe(false);
    });

    it('should return false when element has no data-membership attribute', () => {
      const elementWithoutMembership = document.createElement('div');
      const result = component.elementMatchesMemberships(elementWithoutMembership, ['hoi-vien-inspire']);
      expect(result).toBe(false);
    });

    it('should handle empty target memberships array', () => {
      const result = component.elementMatchesMemberships(element1, []);
      expect(result).toBe(false);
    });

    it('should return false when element has empty data-membership attribute', () => {
      const elementWithEmptyMembership = document.createElement('div');
      elementWithEmptyMembership.setAttribute('data-membership', '');
      const result = component.elementMatchesMemberships(elementWithEmptyMembership, ['hoi-vien-inspire']);
      expect(result).toBe(false);
    });

    it('should return false when element has whitespace-only data-membership attribute', () => {
      const elementWithWhitespaceMembership = document.createElement('div');
      elementWithWhitespaceMembership.setAttribute('data-membership', '   ');
      const result = component.elementMatchesMemberships(elementWithWhitespaceMembership, ['hoi-vien-inspire']);
      expect(result).toBe(false);
    });

    it('should handle element with single comma in data-membership', () => {
      const elementWithComma = document.createElement('div');
      elementWithComma.setAttribute('data-membership', ',');
      const result = component.elementMatchesMemberships(elementWithComma, ['hoi-vien-inspire']);
      expect(result).toBe(false);
    });

    it('should handle element with multiple commas in data-membership', () => {
      const elementWithCommas = document.createElement('div');
      elementWithCommas.setAttribute('data-membership', 'hoi-vien-inspire,,,hoi-vien-priority');
      const result = component.elementMatchesMemberships(elementWithCommas, ['hoi-vien-inspire']);
      expect(result).toBe(true);
    });

    it('should handle element with whitespace around memberships', () => {
      const elementWithWhitespace = document.createElement('div');
      elementWithWhitespace.setAttribute('data-membership', ' hoi-vien-inspire , hoi-vien-priority ');
      const result = component.elementMatchesMemberships(elementWithWhitespace, ['hoi-vien-inspire']);
      expect(result).toBe(true);
    });

    it('should handle null element parameter', () => {
      expect(() => component.elementMatchesMemberships(null, ['hoi-vien-inspire'])).toThrow();
    });

    it('should handle undefined element parameter', () => {
      expect(() => component.elementMatchesMemberships(undefined, ['hoi-vien-inspire'])).toThrow();
    });

    it('should handle null target memberships parameter', () => {
      expect(() => component.elementMatchesMemberships(element1, null)).toThrow();
    });

    it('should handle undefined target memberships parameter', () => {
      expect(() => component.elementMatchesMemberships(element1, undefined)).toThrow();
    });

    it('should be case sensitive for membership matching', () => {
      const result = component.elementMatchesMemberships(element2, ['HOI-VIEN-INSPIRE']);
      expect(result).toBe(false);
    });

    it('should handle very long membership names in element', () => {
      const longMembership = 'a'.repeat(100);
      const elementWithLongMembership = document.createElement('div');
      elementWithLongMembership.setAttribute('data-membership', longMembership);
      const result = component.elementMatchesMemberships(elementWithLongMembership, [longMembership]);
      expect(result).toBe(true);
    });
  });

  describe('showAllElements', () => {
    beforeEach(() => {
      // Hide some elements first
      mockElements.forEach(element => {
        element.style.display = 'none';
        element.classList.add('hidden-forced');
      });
    });

    it('should show all elements with data-membership attributes', () => {
      component.showAllElements();

      mockElements.forEach(element => {
        expect(element.style.display).toBe('');
        expect(element.classList.contains('hidden-forced')).toBe(false);
      });
    });

    it('should handle elements that are already visible', () => {
      // Make some elements already visible
      mockElements[0].style.display = '';
      mockElements[0].classList.remove('hidden-forced');

      component.showAllElements();

      mockElements.forEach(element => {
        expect(element.style.display).toBe('');
        expect(element.classList.contains('hidden-forced')).toBe(false);
      });
    });

    it('should handle empty DOM', () => {
      document.body.innerHTML = '';
      expect(() => component.showAllElements()).not.toThrow();
    });

    it('should handle elements with different display styles', () => {
      mockElements[0].style.display = 'block';
      mockElements[1].style.display = 'inline';
      mockElements[2].style.display = 'flex';

      component.showAllElements();

      mockElements.forEach(element => {
        expect(element.style.display).toBe('');
        expect(element.classList.contains('hidden-forced')).toBe(false);
      });
    });

    it('should handle elements with multiple CSS classes', () => {
      mockElements.forEach(element => {
        element.classList.add('other-class', 'another-class', 'hidden-forced');
      });

      component.showAllElements();

      mockElements.forEach(element => {
        expect(element.classList.contains('hidden-forced')).toBe(false);
        expect(element.classList.contains('other-class')).toBe(true);
        expect(element.classList.contains('another-class')).toBe(true);
      });
    });
  });

  describe('filterElementsByMembership', () => {
    it('should show only matching elements', () => {
      component.filterElementsByMembership(['hoi-vien-inspire']);

      const element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      const element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');
      const element3 = document.querySelector('[data-membership="hoi-vien-priority"]');
      const element4 = document.querySelector('[data-membership="hoi-vien-private"]');

      // Should show elements that match
      expect(element1.style.display).toBe('');
      expect(element1.classList.contains('hidden-forced')).toBe(false);
      expect(element2.style.display).toBe('');
      expect(element2.classList.contains('hidden-forced')).toBe(false);

      // Should hide elements that don't match
      expect(element3.style.display).toBe('none');
      expect(element3.classList.contains('hidden-forced')).toBe(true);
      expect(element4.style.display).toBe('none');
      expect(element4.classList.contains('hidden-forced')).toBe(true);
    });

    it('should handle multiple target memberships', () => {
      component.filterElementsByMembership(['hoi-vien-inspire', 'hoi-vien-private']);

      const element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      const element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');
      const element3 = document.querySelector('[data-membership="hoi-vien-priority"]');
      const element4 = document.querySelector('[data-membership="hoi-vien-private"]');

      // Should show elements that match any target
      expect(element1.style.display).toBe('');
      expect(element2.style.display).toBe('');
      expect(element4.style.display).toBe('');

      // Should hide elements that don't match any target
      expect(element3.style.display).toBe('none');
    });

    it('should handle empty target memberships array', () => {
      component.filterElementsByMembership([]);

      // All elements should be hidden when no targets
      mockElements.forEach(element => {
        expect(element.style.display).toBe('none');
        expect(element.classList.contains('hidden-forced')).toBe(true);
      });
    });

    it('should handle null target memberships parameter', () => {
      expect(() => component.filterElementsByMembership(null)).toThrow();
    });

    it('should handle undefined target memberships parameter', () => {
      expect(() => component.filterElementsByMembership(undefined)).toThrow();
    });

    it('should preserve existing CSS classes when hiding elements', () => {
      mockElements.forEach(element => {
        element.classList.add('existing-class');
      });

      component.filterElementsByMembership(['non-existent-membership']);

      mockElements.forEach(element => {
        expect(element.classList.contains('existing-class')).toBe(true);
        expect(element.classList.contains('hidden-forced')).toBe(true);
      });
    });

    it('should preserve existing CSS classes when showing elements', () => {
      mockElements.forEach(element => {
        element.classList.add('existing-class');
      });

      component.filterElementsByMembership(['hoi-vien-inspire']);

      const element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      const element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');

      expect(element1.classList.contains('existing-class')).toBe(true);
      expect(element1.classList.contains('hidden-forced')).toBe(false);
      expect(element2.classList.contains('existing-class')).toBe(true);
      expect(element2.classList.contains('hidden-forced')).toBe(false);
    });

    it('should handle elements with existing display styles', () => {
      mockElements[0].style.display = 'block';
      mockElements[1].style.display = 'inline-block';

      component.filterElementsByMembership(['hoi-vien-inspire']);

      const element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      const element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');

      // Matching elements should have display reset to empty string
      expect(element1.style.display).toBe('');
      expect(element2.style.display).toBe('');
    });

    it('should handle empty DOM', () => {
      document.body.innerHTML = '';
      expect(() => component.filterElementsByMembership(['hoi-vien-inspire'])).not.toThrow();
    });

    it('should handle case-sensitive membership matching', () => {
      component.filterElementsByMembership(['HOI-VIEN-INSPIRE']);

      // All elements should be hidden since membership matching is case-sensitive
      mockElements.forEach(element => {
        expect(element.style.display).toBe('none');
        expect(element.classList.contains('hidden-forced')).toBe(true);
      });
    });

    it('should handle very long membership names', () => {
      const longMembership = 'a'.repeat(100);
      const elementWithLongMembership = document.createElement('div');
      elementWithLongMembership.setAttribute('data-membership', longMembership);
      document.body.appendChild(elementWithLongMembership);

      component.filterElementsByMembership([longMembership]);

      expect(elementWithLongMembership.style.display).toBe('');
      expect(elementWithLongMembership.classList.contains('hidden-forced')).toBe(false);
    });
  });

  describe('applyMembershipFilter', () => {
    it('should show all elements when no URL parameters', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({});
      const showAllSpy = jest.spyOn(component, 'showAllElements');

      component.applyMembershipFilter();

      expect(showAllSpy).toHaveBeenCalled();
    });

    it('should apply filtering when memberships parameter exists', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });
      const filterSpy = jest.spyOn(component, 'filterElementsByMembership');

      component.applyMembershipFilter();

      expect(filterSpy).toHaveBeenCalledWith(['hoi-vien-inspire']);
    });

    it('should show all elements when memberships parameter is empty', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: ''
      });
      const showAllSpy = jest.spyOn(component, 'showAllElements');

      component.applyMembershipFilter();

      expect(showAllSpy).toHaveBeenCalled();
    });

    it('should show all elements when memberships parameter is null', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: null
      });
      const showAllSpy = jest.spyOn(component, 'showAllElements');

      component.applyMembershipFilter();

      expect(showAllSpy).toHaveBeenCalled();
    });

    it('should show all elements when memberships parameter is undefined', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: undefined
      });
      const showAllSpy = jest.spyOn(component, 'showAllElements');

      component.applyMembershipFilter();

      expect(showAllSpy).toHaveBeenCalled();
    });

    it('should show all elements when memberships parameter is whitespace only', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: '   '
      });
      const showAllSpy = jest.spyOn(component, 'showAllElements');

      component.applyMembershipFilter();

      expect(showAllSpy).toHaveBeenCalled();
    });

    it('should handle LocationUtil errors gracefully', () => {
      LocationUtil.getUrlParamObj.mockImplementation(() => {
        throw new Error('URL parsing error');
      });

      expect(() => component.applyMembershipFilter()).toThrow('URL parsing error');
    });

    it('should handle multiple memberships correctly', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire%2Choi+vien+priority%2Choi+vien+private'
      });
      const filterSpy = jest.spyOn(component, 'filterElementsByMembership');

      component.applyMembershipFilter();

      expect(filterSpy).toHaveBeenCalledWith(['hoi-vien-inspire', 'hoi-vien-priority', 'hoi-vien-private']);
    });

    it('should not call showAllElements when valid memberships exist', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });
      const showAllSpy = jest.spyOn(component, 'showAllElements');
      const filterSpy = jest.spyOn(component, 'filterElementsByMembership');

      component.applyMembershipFilter();

      expect(showAllSpy).not.toHaveBeenCalled();
      expect(filterSpy).toHaveBeenCalled();
    });

    it('should not call filterElementsByMembership when no valid memberships exist', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: ''
      });
      const showAllSpy = jest.spyOn(component, 'showAllElements');
      const filterSpy = jest.spyOn(component, 'filterElementsByMembership');

      component.applyMembershipFilter();

      expect(showAllSpy).toHaveBeenCalled();
      expect(filterSpy).not.toHaveBeenCalled();
    });
  });

  describe('integration tests', () => {
    it('should filter correctly with real URL parameter scenario', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire%2Choi+vien+priority'
      });

      component.applyMembershipFilter();

      const element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      const element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');
      const element3 = document.querySelector('[data-membership="hoi-vien-priority"]');
      const element4 = document.querySelector('[data-membership="hoi-vien-private"]');

      // Elements matching inspire or priority should be visible
      expect(element1.style.display).toBe('');
      expect(element2.style.display).toBe('');
      expect(element3.style.display).toBe('');

      // Element with only private should be hidden
      expect(element4.style.display).toBe('none');
    });

    it('should handle complete workflow from initialization to filtering', () => {
      // Mock URL with memberships
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });

      // Create new component to test full initialization
      new TcbResultPageMembership();

      const element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      const element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');
      const element3 = document.querySelector('[data-membership="hoi-vien-priority"]');
      const element4 = document.querySelector('[data-membership="hoi-vien-private"]');

      // Should show matching elements
      expect(element1.style.display).toBe('');
      expect(element2.style.display).toBe('');

      // Should hide non-matching elements
      expect(element3.style.display).toBe('none');
      expect(element4.style.display).toBe('none');
    });

    it('should handle complex URL encoding scenarios', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi%20vien%20inspire%2Choi%20vien%20priority%2Choi-vien-private'
      });

      component.applyMembershipFilter();

      const element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      const element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');
      const element3 = document.querySelector('[data-membership="hoi-vien-priority"]');
      const element4 = document.querySelector('[data-membership="hoi-vien-private"]');

      // All elements should be visible as they match the decoded memberships
      expect(element1.style.display).toBe('');
      expect(element2.style.display).toBe('');
      expect(element3.style.display).toBe('');
      expect(element4.style.display).toBe('');
    });

    it('should handle dynamic DOM changes', () => {
      // Initial filtering
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });
      component.applyMembershipFilter();

      // Add new element to DOM
      const newElement = document.createElement('div');
      newElement.setAttribute('data-membership', 'hoi-vien-inspire');
      document.body.appendChild(newElement);

      // Re-apply filtering
      component.applyMembershipFilter();

      // New element should be visible
      expect(newElement.style.display).toBe('');
      expect(newElement.classList.contains('hidden-forced')).toBe(false);
    });

    it('should handle switching between different membership filters', () => {
      // First filter
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });
      component.applyMembershipFilter();

      const element1 = document.querySelector('[data-membership="hoi-vien-priority,hoi-vien-inspire"]');
      const element3 = document.querySelector('[data-membership="hoi-vien-priority"]');

      expect(element1.style.display).toBe('');
      expect(element3.style.display).toBe('none');

      // Switch to different filter
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+priority'
      });
      component.applyMembershipFilter();

      // Results should change
      expect(element1.style.display).toBe('');
      expect(element3.style.display).toBe('');
    });

    it('should handle switching from filtering to showing all', () => {
      // Start with filtering
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });
      component.applyMembershipFilter();

      const element3 = document.querySelector('[data-membership="hoi-vien-priority"]');
      expect(element3.style.display).toBe('none');

      // Switch to showing all
      LocationUtil.getUrlParamObj.mockReturnValue({});
      component.applyMembershipFilter();

      // All elements should be visible
      mockElements.forEach(element => {
        expect(element.style.display).toBe('');
        expect(element.classList.contains('hidden-forced')).toBe(false);
      });
    });

    it('should handle malformed URL parameters gracefully', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: '%GG%ZZ%invalid'
      });

      // Should throw error due to malformed URI
      expect(() => component.applyMembershipFilter()).toThrow();
    });

    it('should handle very large number of elements', () => {
      // Create many elements
      const manyElements = [];
      for (let i = 0; i < 100; i++) {
        const element = document.createElement('div');
        let membership;
        if (i % 2 === 0) {
          membership = 'hoi-vien-inspire';
        } else {
          membership = 'hoi-vien-priority';
        }
        element.setAttribute('data-membership', membership);
        document.body.appendChild(element);
        manyElements.push(element);
      }

      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });

      // Should handle large number of elements without performance issues
      const startTime = Date.now();
      component.applyMembershipFilter();
      const endTime = Date.now();

      // Should complete in reasonable time (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100);

      // Check that filtering worked correctly
      manyElements.forEach((element, index) => {
        if (index % 2 === 0) {
          expect(element.style.display).toBe('');
        } else {
          expect(element.style.display).toBe('none');
        }
      });
    });

    it('should handle concurrent filter applications', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+inspire'
      });

      // Apply filter multiple times rapidly
      component.applyMembershipFilter();
      component.applyMembershipFilter();
      component.applyMembershipFilter();

      // Should still work correctly
      const element2 = document.querySelector('[data-membership="hoi-vien-inspire"]');
      const element3 = document.querySelector('[data-membership="hoi-vien-priority"]');

      expect(element2.style.display).toBe('');
      expect(element3.style.display).toBe('none');
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle document.querySelectorAll returning null', () => {
      const originalQuerySelectorAll = document.querySelectorAll;
      document.querySelectorAll = jest.fn().mockReturnValue(null);

      expect(() => component.getMembershipElements()).not.toThrow();

      document.querySelectorAll = originalQuerySelectorAll;
    });

    it('should handle element.getAttribute returning null', () => {
      const element = document.createElement('div');
      element.getAttribute = jest.fn().mockReturnValue(null);

      const result = component.elementMatchesMemberships(element, ['test']);
      expect(result).toBe(false);
    });

    it('should handle element.style being undefined', () => {
      const element = document.createElement('div');
      element.setAttribute('data-membership', 'hoi-vien-inspire');
      delete element.style;

      expect(() => component.filterElementsByMembership(['hoi-vien-inspire'])).not.toThrow();
    });

    it('should handle element.classList being undefined', () => {
      const element = document.createElement('div');
      element.setAttribute('data-membership', 'hoi-vien-inspire');
      delete element.classList;

      expect(() => component.filterElementsByMembership(['hoi-vien-inspire'])).not.toThrow();
    });

    it('should handle decodeURIComponent throwing error', () => {
      const originalDecodeURIComponent = global.decodeURIComponent;
      global.decodeURIComponent = jest.fn().mockImplementation(() => {
        throw new Error('Decode error');
      });

      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'invalid%encoding'
      });

      expect(() => component.parseUrlMemberships()).toThrow();

      global.decodeURIComponent = originalDecodeURIComponent;
    });
  });

  describe('show more functionality', () => {
    beforeEach(() => {
      // Create a component with view more button and 5 cards for testing
      document.body.innerHTML = `
        <tcb-result-page-membership>
          <div class="card" data-membership="hoi-vien-priority">Card 1</div>
          <div class="card" data-membership="hoi-vien-inspire">Card 2</div>
          <div class="card" data-membership="hoi-vien-priority">Card 3</div>
          <div class="card" data-membership="hoi-vien-private">Card 4</div>
          <div class="card" data-membership="hoi-vien-premium">Card 5</div>
          <div class="tcb-result-page-membership__view-more">
            <span>Xem hết các ưu đãi</span>
          </div>
        </tcb-result-page-membership>
      `;

      // Create component instance
      component = new TcbResultPageMembership();
    });

    it('should initialize with first 3 cards visible and rest hidden', () => {
      const cards = component.getAllCards();
      const viewMoreButton = component.getViewMoreButton();

      expect(cards.length).toBe(5);
      expect(viewMoreButton).toBeTruthy();

      // First 3 cards should not have hidden class
      expect(cards[0].classList.contains('hidden-forced')).toBe(false);
      expect(cards[1].classList.contains('hidden-forced')).toBe(false);
      expect(cards[2].classList.contains('hidden-forced')).toBe(false);

      // Last 2 cards should have hidden class
      expect(cards[3].classList.contains('hidden-forced')).toBe(true);
      expect(cards[4].classList.contains('hidden-forced')).toBe(true);
    });

    it('should show all cards when view more button is clicked', () => {
      const cards = component.getAllCards();
      const viewMoreButton = component.getViewMoreButton();

      // Click view more button
      viewMoreButton.click();

      // All cards should be visible
      cards.forEach(card => {
        expect(card.classList.contains('hidden-forced')).toBe(false);
      });

      // Button text should change
      const spanElement = viewMoreButton.querySelector('span');
      expect(spanElement.textContent).toBe('Thu gọn');
    });

    it('should hide cards again when view more button is clicked twice', () => {
      const cards = component.getAllCards();
      const viewMoreButton = component.getViewMoreButton();

      // Click view more button twice
      viewMoreButton.click();
      viewMoreButton.click();

      // First 3 cards should be visible
      expect(cards[0].classList.contains('hidden-forced')).toBe(false);
      expect(cards[1].classList.contains('hidden-forced')).toBe(false);
      expect(cards[2].classList.contains('hidden-forced')).toBe(false);

      // Last 2 cards should be hidden
      expect(cards[3].classList.contains('hidden-forced')).toBe(true);
      expect(cards[4].classList.contains('hidden-forced')).toBe(true);

      // Button text should reset
      const spanElement = viewMoreButton.querySelector('span');
      expect(spanElement.textContent).toBe('Xem hết các ưu đãi');
    });

    it('should hide view more button when 3 or fewer cards', () => {
      // Create component with only 3 cards
      document.body.innerHTML = `
        <tcb-result-page-membership>
          <div class="card" data-membership="hoi-vien-priority">Card 1</div>
          <div class="card" data-membership="hoi-vien-inspire">Card 2</div>
          <div class="card" data-membership="hoi-vien-priority">Card 3</div>
          <div class="tcb-result-page-membership__view-more">
            <span>Xem hết các ưu đãi</span>
          </div>
        </tcb-result-page-membership>
      `;

      component = new TcbResultPageMembership();
      const viewMoreButton = component.getViewMoreButton();

      expect(viewMoreButton.classList.contains('hidden-forced')).toBe(true);
    });

    it('should work correctly with membership filtering', () => {
      LocationUtil.getUrlParamObj.mockReturnValue({
        memberships: 'hoi+vien+priority'
      });

      component = new TcbResultPageMembership();
      const cards = component.getAllCards();
      const visibleCards = component.getVisibleCards();

      // Only priority cards should be visible (cards 0 and 2)
      expect(visibleCards.length).toBe(2);
      expect(cards[0].classList.contains('hidden-forced')).toBe(false);
      expect(cards[1].classList.contains('hidden-forced')).toBe(true);
      expect(cards[2].classList.contains('hidden-forced')).toBe(false);
      expect(cards[3].classList.contains('hidden-forced')).toBe(true);
      expect(cards[4].classList.contains('hidden-forced')).toBe(true);

      // View more button should be hidden since only 2 visible cards
      const viewMoreButton = component.getViewMoreButton();
      expect(viewMoreButton.classList.contains('hidden-forced')).toBe(true);
    });
  });

  describe('count filter functionality', () => {
    describe('getCountFilterElement', () => {
      it('should return the count filter element', () => {
        const countElement = component.getCountFilterElement();
        expect(countElement).toBeTruthy();
        expect(countElement.classList.contains('count-filter-membership')).toBe(true);
      });

      it('should return null when count filter element does not exist', () => {
        document.querySelector('.count-filter-membership').remove();
        const countElement = component.getCountFilterElement();
        expect(countElement).toBeNull();
      });
    });

    describe('countFilteredCards', () => {
      it('should return 0 when no URL parameters are present', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({});
        const count = component.countFilteredCards();
        expect(count).toBe(0);
      });

      it('should return correct count when filters are applied', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({
          memberships: 'hoi+vien+inspire'
        });
        const count = component.countFilteredCards();
        expect(count).toBe(2); // Cards 1 and 2 match hoi-vien-inspire
      });

      it('should return correct count for multiple membership filters', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({
          memberships: 'hoi+vien+inspire%2Choi+vien+priority'
        });
        const count = component.countFilteredCards();
        expect(count).toBe(3); // Cards 1, 2, and 3 match
      });

      it('should return 0 when no cards match the filter', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({
          memberships: 'non-existent-membership'
        });
        const count = component.countFilteredCards();
        expect(count).toBe(0);
      });
    });

    describe('updateCountDisplay', () => {
      it('should update count element with correct value', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({
          memberships: 'hoi+vien+inspire'
        });
        component.updateCountDisplay();

        const countElement = component.getCountFilterElement();
        expect(countElement.textContent).toBe('2');
      });

      it('should update count to 0 when no filters are active', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({});
        component.updateCountDisplay();

        const countElement = component.getCountFilterElement();
        expect(countElement.textContent).toBe('0');
      });

      it('should handle missing count element gracefully', () => {
        document.querySelector('.count-filter-membership').remove();
        expect(() => component.updateCountDisplay()).not.toThrow();
      });

      it('should update count when filters change', () => {
        // Start with one filter
        LocationUtil.getUrlParamObj.mockReturnValue({
          memberships: 'hoi+vien+inspire'
        });
        component.updateCountDisplay();

        let countElement = component.getCountFilterElement();
        expect(countElement.textContent).toBe('2');

        // Change to different filter
        LocationUtil.getUrlParamObj.mockReturnValue({
          memberships: 'hoi+vien+priority'
        });
        component.updateCountDisplay();

        countElement = component.getCountFilterElement();
        expect(countElement.textContent).toBe('2'); // Cards 1 and 3 match priority
      });
    });

    describe('integration with filtering methods', () => {
      it('should update count when applyAllFilters is called', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({
          memberships: 'hoi+vien+inspire'
        });

        const updateCountSpy = jest.spyOn(component, 'updateCountDisplay');
        component.applyAllFilters();

        expect(updateCountSpy).toHaveBeenCalled();

        const countElement = component.getCountFilterElement();
        expect(countElement.textContent).toBe('2');
      });

      it('should update count during initialization with URL parameters', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({
          memberships: 'hoi+vien+priority'
        });

        // Create new component to test initialization
        const newComponent = new TcbResultPageMembership();
        const countElement = newComponent.getCountFilterElement();
        expect(countElement.textContent).toBe('2');
      });

      it('should update count during initialization without URL parameters', () => {
        LocationUtil.getUrlParamObj.mockReturnValue({});

        // Create new component to test initialization
        const newComponent = new TcbResultPageMembership();
        const countElement = newComponent.getCountFilterElement();
        expect(countElement.textContent).toBe('0');
      });
    });
  });
});
